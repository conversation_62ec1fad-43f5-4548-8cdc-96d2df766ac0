"""
性能指标计算器 - 计算各种交易和投资组合性能指标
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from scipy import stats

logger = logging.getLogger(__name__)

class PerformanceMetrics:
    """性能指标计算器"""
    
    def __init__(self):
        self.trading_days_per_year = 252
        
    def calculate_metrics(self, portfolio_values: List[float], 
                         returns: List[float] = None,
                         positions: List[float] = None,
                         trades: List[Dict] = None,
                         benchmark_returns: List[float] = None) -> Dict[str, float]:
        """
        计算综合性能指标
        
        Args:
            portfolio_values: 组合价值序列
            returns: 收益率序列
            positions: 持仓序列
            trades: 交易记录
            benchmark_returns: 基准收益率
            
        Returns:
            性能指标字典
        """
        metrics = {}
        
        if len(portfolio_values) < 2:
            return metrics
        
        # 计算收益率（如果未提供）
        if returns is None:
            returns = self._calculate_returns(portfolio_values)
        
        returns = np.array(returns)
        
        # 基础收益指标
        metrics.update(self._calculate_return_metrics(returns))
        
        # 风险指标
        metrics.update(self._calculate_risk_metrics(returns, portfolio_values))
        
        # 风险调整收益指标
        metrics.update(self._calculate_risk_adjusted_metrics(returns))
        
        # 回撤指标
        metrics.update(self._calculate_drawdown_metrics(portfolio_values))
        
        # 交易指标
        if trades:
            metrics.update(self._calculate_trading_metrics(trades))
        
        # 基准比较指标
        if benchmark_returns:
            metrics.update(self._calculate_benchmark_metrics(returns, benchmark_returns))
        
        return metrics
    
    def _calculate_returns(self, portfolio_values: List[float]) -> List[float]:
        """计算收益率"""
        values = np.array(portfolio_values)
        returns = (values[1:] / values[:-1]) - 1
        return returns.tolist()
    
    def _calculate_return_metrics(self, returns: np.ndarray) -> Dict[str, float]:
        """计算收益指标"""
        metrics = {}
        
        # 总收益率
        total_return = (1 + returns).prod() - 1
        metrics['total_return'] = total_return
        
        # 年化收益率
        n_periods = len(returns)
        if n_periods > 0:
            annualized_return = (1 + total_return) ** (self.trading_days_per_year / n_periods) - 1
            metrics['annualized_return'] = annualized_return
        
        # 平均日收益率
        metrics['mean_daily_return'] = np.mean(returns)
        
        # 胜率
        win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0
        metrics['win_rate'] = win_rate
        
        # 盈亏比
        winning_returns = returns[returns > 0]
        losing_returns = returns[returns < 0]
        
        if len(winning_returns) > 0 and len(losing_returns) > 0:
            profit_loss_ratio = np.mean(winning_returns) / abs(np.mean(losing_returns))
            metrics['profit_loss_ratio'] = profit_loss_ratio
        else:
            metrics['profit_loss_ratio'] = 0.0
        
        return metrics
    
    def _calculate_risk_metrics(self, returns: np.ndarray, 
                               portfolio_values: List[float]) -> Dict[str, float]:
        """计算风险指标"""
        metrics = {}
        
        # 波动率
        volatility = np.std(returns)
        metrics['volatility'] = volatility
        metrics['annualized_volatility'] = volatility * np.sqrt(self.trading_days_per_year)
        
        # VaR和CVaR
        metrics['var_95'] = np.percentile(returns, 5)
        metrics['var_99'] = np.percentile(returns, 1)
        
        var_95 = metrics['var_95']
        cvar_95 = returns[returns <= var_95].mean() if np.any(returns <= var_95) else 0
        metrics['cvar_95'] = cvar_95
        
        # 偏度和峰度
        metrics['skewness'] = stats.skew(returns)
        metrics['kurtosis'] = stats.kurtosis(returns)
        
        # 下行风险
        downside_returns = returns[returns < 0]
        if len(downside_returns) > 0:
            metrics['downside_deviation'] = np.std(downside_returns)
            metrics['annualized_downside_deviation'] = metrics['downside_deviation'] * np.sqrt(self.trading_days_per_year)
        else:
            metrics['downside_deviation'] = 0.0
            metrics['annualized_downside_deviation'] = 0.0
        
        return metrics
    
    def _calculate_risk_adjusted_metrics(self, returns: np.ndarray) -> Dict[str, float]:
        """计算风险调整收益指标"""
        metrics = {}
        
        mean_return = np.mean(returns)
        volatility = np.std(returns)
        
        # 夏普比率
        if volatility > 0:
            sharpe_ratio = mean_return / volatility * np.sqrt(self.trading_days_per_year)
            metrics['sharpe_ratio'] = sharpe_ratio
        else:
            metrics['sharpe_ratio'] = 0.0
        
        # Sortino比率
        downside_returns = returns[returns < 0]
        if len(downside_returns) > 0:
            downside_deviation = np.std(downside_returns)
            sortino_ratio = mean_return / downside_deviation * np.sqrt(self.trading_days_per_year)
            metrics['sortino_ratio'] = sortino_ratio
        else:
            metrics['sortino_ratio'] = 0.0
        
        # Calmar比率
        max_drawdown = self._calculate_max_drawdown_from_returns(returns)
        if max_drawdown > 0:
            annualized_return = mean_return * self.trading_days_per_year
            calmar_ratio = annualized_return / max_drawdown
            metrics['calmar_ratio'] = calmar_ratio
        else:
            metrics['calmar_ratio'] = 0.0
        
        return metrics
    
    def _calculate_drawdown_metrics(self, portfolio_values: List[float]) -> Dict[str, float]:
        """计算回撤指标"""
        metrics = {}
        
        values = np.array(portfolio_values)
        
        # 计算回撤序列
        peak = np.maximum.accumulate(values)
        drawdown = (peak - values) / peak
        
        # 最大回撤
        metrics['max_drawdown'] = np.max(drawdown)
        
        # 平均回撤
        metrics['average_drawdown'] = np.mean(drawdown[drawdown > 0]) if np.any(drawdown > 0) else 0
        
        # 回撤持续时间
        drawdown_periods = self._calculate_drawdown_periods(drawdown)
        if drawdown_periods:
            metrics['max_drawdown_duration'] = max(drawdown_periods)
            metrics['average_drawdown_duration'] = np.mean(drawdown_periods)
        else:
            metrics['max_drawdown_duration'] = 0
            metrics['average_drawdown_duration'] = 0
        
        return metrics
    
    def _calculate_max_drawdown_from_returns(self, returns: np.ndarray) -> float:
        """从收益率计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        peak = np.maximum.accumulate(cumulative)
        drawdown = (peak - cumulative) / peak
        return np.max(drawdown)
    
    def _calculate_drawdown_periods(self, drawdown: np.ndarray) -> List[int]:
        """计算回撤持续期间"""
        periods = []
        in_drawdown = False
        current_period = 0
        
        for dd in drawdown:
            if dd > 0:
                if not in_drawdown:
                    in_drawdown = True
                    current_period = 1
                else:
                    current_period += 1
            else:
                if in_drawdown:
                    periods.append(current_period)
                    in_drawdown = False
                    current_period = 0
        
        # 如果最后还在回撤中
        if in_drawdown:
            periods.append(current_period)
        
        return periods
    
    def _calculate_trading_metrics(self, trades: List[Dict]) -> Dict[str, float]:
        """计算交易指标"""
        metrics = {}
        
        if not trades:
            return metrics
        
        # 交易次数
        metrics['total_trades'] = len(trades)
        
        # 盈利交易和亏损交易
        profitable_trades = [t for t in trades if t.get('profit', 0) > 0]
        losing_trades = [t for t in trades if t.get('profit', 0) < 0]
        
        metrics['profitable_trades'] = len(profitable_trades)
        metrics['losing_trades'] = len(losing_trades)
        
        # 交易胜率
        if len(trades) > 0:
            metrics['trade_win_rate'] = len(profitable_trades) / len(trades)
        
        # 平均盈利和亏损
        if profitable_trades:
            metrics['average_profit'] = np.mean([t['profit'] for t in profitable_trades])
        else:
            metrics['average_profit'] = 0.0
        
        if losing_trades:
            metrics['average_loss'] = np.mean([t['profit'] for t in losing_trades])
        else:
            metrics['average_loss'] = 0.0
        
        # 最大单笔盈利和亏损
        all_profits = [t.get('profit', 0) for t in trades]
        if all_profits:
            metrics['max_profit'] = max(all_profits)
            metrics['max_loss'] = min(all_profits)
        
        return metrics
    
    def _calculate_benchmark_metrics(self, returns: np.ndarray, 
                                   benchmark_returns: List[float]) -> Dict[str, float]:
        """计算基准比较指标"""
        metrics = {}
        
        benchmark_returns = np.array(benchmark_returns)
        
        # 确保长度一致
        min_length = min(len(returns), len(benchmark_returns))
        returns = returns[:min_length]
        benchmark_returns = benchmark_returns[:min_length]
        
        # 超额收益
        excess_returns = returns - benchmark_returns
        metrics['excess_return'] = np.mean(excess_returns)
        metrics['annualized_excess_return'] = metrics['excess_return'] * self.trading_days_per_year
        
        # 信息比率
        if np.std(excess_returns) > 0:
            information_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(self.trading_days_per_year)
            metrics['information_ratio'] = information_ratio
        else:
            metrics['information_ratio'] = 0.0
        
        # Beta
        if np.var(benchmark_returns) > 0:
            beta = np.cov(returns, benchmark_returns)[0, 1] / np.var(benchmark_returns)
            metrics['beta'] = beta
            
            # Alpha
            risk_free_rate = 0.02 / self.trading_days_per_year  # 假设2%无风险利率
            alpha = np.mean(returns) - risk_free_rate - beta * (np.mean(benchmark_returns) - risk_free_rate)
            metrics['alpha'] = alpha * self.trading_days_per_year
        else:
            metrics['beta'] = 0.0
            metrics['alpha'] = 0.0
        
        # 相关系数
        if len(returns) > 1 and len(benchmark_returns) > 1:
            correlation = np.corrcoef(returns, benchmark_returns)[0, 1]
            metrics['correlation'] = correlation if not np.isnan(correlation) else 0.0
        else:
            metrics['correlation'] = 0.0
        
        return metrics
    
    def generate_performance_report(self, metrics: Dict[str, float]) -> str:
        """生成性能报告"""
        report = "=== 投资组合性能报告 ===\n\n"
        
        # 收益指标
        report += "收益指标:\n"
        report += f"  总收益率: {metrics.get('total_return', 0):.2%}\n"
        report += f"  年化收益率: {metrics.get('annualized_return', 0):.2%}\n"
        report += f"  胜率: {metrics.get('win_rate', 0):.2%}\n"
        report += f"  盈亏比: {metrics.get('profit_loss_ratio', 0):.2f}\n\n"
        
        # 风险指标
        report += "风险指标:\n"
        report += f"  年化波动率: {metrics.get('annualized_volatility', 0):.2%}\n"
        report += f"  最大回撤: {metrics.get('max_drawdown', 0):.2%}\n"
        report += f"  VaR(95%): {metrics.get('var_95', 0):.2%}\n"
        report += f"  CVaR(95%): {metrics.get('cvar_95', 0):.2%}\n\n"
        
        # 风险调整收益
        report += "风险调整收益:\n"
        report += f"  夏普比率: {metrics.get('sharpe_ratio', 0):.2f}\n"
        report += f"  Sortino比率: {metrics.get('sortino_ratio', 0):.2f}\n"
        report += f"  Calmar比率: {metrics.get('calmar_ratio', 0):.2f}\n\n"
        
        # 交易指标
        if 'total_trades' in metrics:
            report += "交易指标:\n"
            report += f"  总交易次数: {metrics.get('total_trades', 0)}\n"
            report += f"  交易胜率: {metrics.get('trade_win_rate', 0):.2%}\n"
            report += f"  平均盈利: {metrics.get('average_profit', 0):.2%}\n"
            report += f"  平均亏损: {metrics.get('average_loss', 0):.2%}\n\n"
        
        return report

if __name__ == "__main__":
    # 测试性能指标计算器
    metrics_calculator = PerformanceMetrics()
    
    # 模拟数据
    np.random.seed(42)
    returns = np.random.randn(252) * 0.01  # 一年的日收益率
    portfolio_values = [1000000]
    
    for r in returns:
        portfolio_values.append(portfolio_values[-1] * (1 + r))
    
    # 计算指标
    metrics = metrics_calculator.calculate_metrics(portfolio_values, returns.tolist())
    
    # 生成报告
    report = metrics_calculator.generate_performance_report(metrics)
    print(report)
    
    print("性能指标计算器测试完成！")
