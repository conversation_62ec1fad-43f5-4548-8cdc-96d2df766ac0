"""
SOTA深度强化学习多因子股票投资系统 - 使用示例
演示如何使用系统进行股票投资策略训练和评估
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from config import config
from data.loaders.data_loader import DataLoaderFactory
from data.processors.factor_processor import FactorProcessor
from environments.trading.hierarchical_env import HierarchicalTradingEnvironment
from agents.hierarchical.h_dan_agent import HDANAgent
from visualization.training_monitor import TrainingMonitor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_data():
    """创建示例数据"""
    logger.info("创建示例数据...")
    
    # 生成模拟股票数据
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=500, freq='D')
    
    # 创建两只股票的数据
    stock_codes = ['000001.SZ', '000002.SZ']
    all_data = []
    
    for stock_code in stock_codes:
        # 基础价格数据
        base_price = 100
        returns = np.random.randn(len(dates)) * 0.02
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # 生成OHLC数据
        closes = np.array(prices)
        opens = closes * (1 + np.random.randn(len(closes)) * 0.005)
        highs = np.maximum(opens, closes) * (1 + np.abs(np.random.randn(len(closes))) * 0.01)
        lows = np.minimum(opens, closes) * (1 - np.abs(np.random.randn(len(closes))) * 0.01)
        
        # 成交量和成交额
        volumes = np.random.randint(1000000, 10000000, len(dates))
        amounts = volumes * closes * (1 + np.random.randn(len(dates)) * 0.1)
        
        # 涨跌幅
        pct_chgs = np.concatenate([[0], np.diff(closes) / closes[:-1] * 100])
        
        # 技术指标（简化生成）
        momentum_5d = np.random.randn(len(dates)) * 0.1
        ma_ratio_5d = 1 + np.random.randn(len(dates)) * 0.05
        volatility_5d = np.abs(np.random.randn(len(dates))) * 0.02
        
        momentum_10d = np.random.randn(len(dates)) * 0.15
        ma_ratio_10d = 1 + np.random.randn(len(dates)) * 0.08
        volatility_10d = np.abs(np.random.randn(len(dates))) * 0.03
        
        momentum_20d = np.random.randn(len(dates)) * 0.2
        ma_ratio_20d = 1 + np.random.randn(len(dates)) * 0.1
        volatility_20d = np.abs(np.random.randn(len(dates))) * 0.04
        
        # MACD指标
        macd = np.random.randn(len(dates)) * 0.5
        macd_signal = macd + np.random.randn(len(dates)) * 0.1
        macd_hist = macd - macd_signal
        
        # 其他指标
        bb_ratio = np.random.uniform(0, 1, len(dates))
        atr = np.abs(np.random.randn(len(dates))) * 2
        adaptive_ma = closes * (1 + np.random.randn(len(dates)) * 0.02)
        
        # 目标收益率
        target_return_close = np.concatenate([[0], pct_chgs[1:] / 100])
        target_return_high = target_return_close * (1 + np.random.randn(len(dates)) * 0.1)
        
        # 组装数据
        stock_data = pd.DataFrame({
            'ts_code': stock_code,
            'trade_date': dates,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'pct_chg': pct_chgs,
            'vol': volumes,
            'amount': amounts,
            'target_return_close': target_return_close,
            'target_return_high': target_return_high,
            'momentum_5d': momentum_5d,
            'ma_ratio_5d': ma_ratio_5d,
            'volatility_5d': volatility_5d,
            'momentum_10d': momentum_10d,
            'ma_ratio_10d': ma_ratio_10d,
            'volatility_10d': volatility_10d,
            'momentum_20d': momentum_20d,
            'ma_ratio_20d': ma_ratio_20d,
            'volatility_20d': volatility_20d,
            'macd': macd,
            'macd_signal': macd_signal,
            'macd_hist': macd_hist,
            'bb_ratio': bb_ratio,
            'atr': atr,
            'adaptive_ma': adaptive_ma
        })
        
        all_data.append(stock_data)
    
    # 合并所有数据
    final_data = pd.concat(all_data, ignore_index=True)
    
    # 保存到CSV
    final_data.to_csv('data/sample_data.csv', index=False)
    logger.info(f"示例数据已保存到 data/sample_data.csv，共 {len(final_data)} 条记录")
    
    return final_data

def demonstrate_data_loading():
    """演示数据加载和预处理"""
    logger.info("=== 演示数据加载和预处理 ===")
    
    # 加载数据
    data_loader = DataLoaderFactory.create_loader('data/sample_data.csv')
    raw_data = data_loader.load_data()
    processed_data = data_loader.preprocess_data()
    
    # 显示数据摘要
    summary = data_loader.get_data_summary()
    logger.info(f"数据摘要: {summary}")
    
    # 因子处理
    factor_processor = FactorProcessor()
    enhanced_data = factor_processor.process_factors(processed_data)
    
    # 显示因子摘要
    factor_summary = factor_processor.get_factor_summary()
    logger.info(f"因子处理摘要: {factor_summary}")
    
    return enhanced_data

def demonstrate_environment():
    """演示交易环境"""
    logger.info("=== 演示交易环境 ===")
    
    # 加载数据
    data = pd.read_csv('data/sample_data.csv')
    data['trade_date'] = pd.to_datetime(data['trade_date'])
    data = data.rename(columns={'ts_code': 'stock_code', 'trade_date': 'date'})
    
    # 创建环境
    env = HierarchicalTradingEnvironment(data, '000001.SZ')
    
    # 重置环境
    state = env.reset()
    logger.info(f"初始状态维度: {state.shape}")
    
    # 执行几步随机动作
    total_reward = 0
    for step in range(10):
        action = env.action_space.sample()
        next_state, reward, done, info = env.step(action)
        
        total_reward += reward
        logger.info(f"步骤 {step}: 动作={action['daily_action']}, "
                   f"奖励={reward:.4f}, 组合价值={info['portfolio_value']:.2f}")
        
        if done:
            break
    
    logger.info(f"总奖励: {total_reward:.4f}")
    
    # 获取性能摘要
    performance = env.get_performance_summary()
    logger.info(f"性能摘要: {performance}")

def demonstrate_agent_training():
    """演示智能体训练"""
    logger.info("=== 演示智能体训练 ===")
    
    # 加载数据
    data = pd.read_csv('data/sample_data.csv')
    data['trade_date'] = pd.to_datetime(data['trade_date'])
    data = data.rename(columns={'ts_code': 'stock_code', 'trade_date': 'date'})
    
    # 创建环境和智能体
    env = HierarchicalTradingEnvironment(data, '000001.SZ')
    agent = HDANAgent()
    monitor = TrainingMonitor()
    
    # 简化训练（只训练几个episode）
    episodes = 20
    
    for episode in range(episodes):
        state = env.reset()
        episode_reward = 0
        episode_loss = 0
        step_count = 0
        
        while True:
            # 选择动作
            action = agent.select_action(state, training=True)
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            # 存储经验
            agent.store_experience(state, action, reward, next_state, done)
            
            # 训练（如果有足够经验）
            if len(agent.memory) >= 10:  # 降低最小经验要求用于演示
                loss_info = agent.train_step()
                if loss_info:
                    episode_loss += loss_info.get('total_loss', 0)
            
            state = next_state
            episode_reward += reward
            step_count += 1
            
            if done or step_count > 50:  # 限制步数用于演示
                break
        
        # 记录训练进度
        avg_loss = episode_loss / max(step_count, 1)
        monitor.update(episode, episode_reward, avg_loss, agent.epsilon)
        
        if episode % 5 == 0:
            logger.info(f"Episode {episode}: 奖励={episode_reward:.4f}, "
                       f"损失={avg_loss:.4f}, Epsilon={agent.epsilon:.4f}")
    
    # 生成训练曲线
    fig = monitor.plot_training_curves("000001.SZ", save=True)
    logger.info("训练曲线已保存到 visualization/plots/")
    
    # 保存模型
    agent.save_model('checkpoints/demo_model.pth')
    logger.info("演示模型已保存到 checkpoints/demo_model.pth")

def demonstrate_visualization():
    """演示可视化功能"""
    logger.info("=== 演示可视化功能 ===")
    
    monitor = TrainingMonitor()
    
    # 模拟一些数据
    portfolio_values = [1000000]
    for i in range(100):
        daily_return = np.random.randn() * 0.01
        portfolio_values.append(portfolio_values[-1] * (1 + daily_return))
    
    dates = pd.date_range('2023-01-01', periods=101, freq='D')
    
    # 生成投资组合性能图
    fig1 = monitor.plot_portfolio_performance(
        portfolio_values, 
        dates=[d.strftime('%Y-%m-%d') for d in dates],
        stock_code="DEMO"
    )
    
    # 生成回撤分析图
    fig2 = monitor.plot_drawdown_analysis(
        portfolio_values,
        dates=[d.strftime('%Y-%m-%d') for d in dates],
        stock_code="DEMO"
    )
    
    # 生成因子重要性图
    importance_scores = {
        'momentum_5d': 0.15,
        'ma_ratio_10d': 0.12,
        'volatility_20d': 0.10,
        'macd': 0.08,
        'bb_ratio': 0.07,
        'atr': 0.06
    }
    
    fig3 = monitor.plot_factor_importance(importance_scores, stock_code="DEMO")
    
    logger.info("可视化图表已生成并保存到 visualization/plots/")

def main():
    """主演示函数"""
    logger.info("开始SOTA深度强化学习多因子股票投资系统演示")
    
    # 创建必要目录
    Path('data').mkdir(exist_ok=True)
    Path('checkpoints').mkdir(exist_ok=True)
    Path('visualization/plots').mkdir(parents=True, exist_ok=True)
    
    try:
        # 1. 创建示例数据
        sample_data = create_sample_data()
        
        # 2. 演示数据加载和预处理
        enhanced_data = demonstrate_data_loading()
        
        # 3. 演示交易环境
        demonstrate_environment()
        
        # 4. 演示智能体训练
        demonstrate_agent_training()
        
        # 5. 演示可视化功能
        demonstrate_visualization()
        
        logger.info("=== 演示完成 ===")
        logger.info("您可以查看以下文件:")
        logger.info("- 示例数据: data/sample_data.csv")
        logger.info("- 训练模型: checkpoints/demo_model.pth")
        logger.info("- 可视化图表: visualization/plots/")
        
        logger.info("\n要开始真实训练，请运行:")
        logger.info("python train.py --data_path data/your_real_data.csv")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
