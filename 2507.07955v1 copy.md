# Dynamic Chunking for End-to-End Hierarchical Sequence Modeling

**<PERSON><PERSON><PERSON>**

Carnegie Mellon University

<EMAIL>

**<PERSON>**

Cartesia AI

<EMAIL>

**<PERSON>u**

Carnegie Mellon University, Cartesia AI

<EMAIL>,<EMAIL>

## Abstract

Despite incredible progress in language models (LMs) in recent years, largely resulting from moving away from specialized models designed for specific tasks to general models based on powerful architectures (e.g. the Transformer) that learn everything from raw data, pre-processing steps such as tokenization remain a barrier to true end-to-end foundation models.We introduce a collection of new techniques that enable a **dynamic** **chunking** mechanism which automatically learns content-and context-dependent segmentation strategies learned jointly with the rest of the model. Incorporating this into an explicit **hierarchical** **network** **(H-Net)** allows replacing the (implicitlyhierarchical) tokenization-LM-detokenization pipeline with a single model learned fully end-to-end. When compute-and data-matched, an H-Net with one stage of hierarchy operating at the byte level outperforms a strong Transformer language model operating over BPE tokens.Iterating the hierarchy to multiple stages further increases its performance by modeling multiple levels of abstraction,demonstrating significantly better scaling with data and matching a token-based Transformer of twice its size. H-Nets pretrained on English show significantly increased character-level robustness, and qualitatively learn meaningful data-dependent chunking strategies without any heuristics or explicit supervision. Finally, the H-Net's improvement over tokenized pipelines is further increased in languages and modalities with weaker tokenization heuristics, such as Chinese and code, or DNA sequences (nearly 4x improvement in data efficiency over baselines), showing the potential of true end-to-end models that learn and scale better from unprocessed data.

## 1 Introduction

A broad goal of deep learning is to learn meaningful patterns from raw data, automatically extracting features and building abstractions in an end-to-end fashion. However, fixed-vocabulary **tokenization,** the process of compressing raw text into predefined chunks through algorithms such as byte-pair encoding (BPE) (Kudo and Richardson 2018;Sennrich,Haddow,and Birch 2015), remains a pervasive handcrafted preprocessing step in modern language models (LMs) (T. Brown et al.2020; Grattafiori et al. 2024). Tokenization comes with a host of well-documented drawbacks, from poor character-level understanding to lack of meaning and interpretability to degraded performance on complex languages and modalities (Ahia,Kumar, Gonen, Kasai, et al. 2023; Belinkov and Bisk 2017; J. H. Clark et al. 2022; Petrov et al. 2023; Sun et al. 2020; Xue et al.2022).1 Replacing the tokenization-LM-detokenization pipeline with a single end-to-end model would also adhere better to the spirit of deep learning, ideally scaling more powerfully with data and parameters (c.f. the bitter lesson) (Perić 2025;Sutton 2019). However, tokenization remains an indispensable component of language models and other sequential data for its ability to compress and shorten sequences; as of yet, no end-to-end tokenizer-free model has matched the performance of tokenizer-based language models when matched for computational budget.

A line of recent works has turned to overcoming tokenization in autoregressive sequence models, which requires addressing a series of difficult technical challenges:2

1Many other edge cases have been discussed in informal online discourse rather than papers; we defer to Andrej Karpathy's lectures and tweets.2An extended related work can be found in Appendix A, which is summarized in Table 6.

<!-- 1 -->

<!-- S70CmnrOL I0T.so] IASS6L0:0sZAAXTg -->

·Direct byte-level language modeling with isotropic architectures3 can be improved with efficient sequence models such as MambaByte (J. Wang et al. 2024), but still incur prohibitive computational costs while underperforming tokenized models in compute-matched settings.

·To improve efficiency, hierarchical architectures such as Hourglass Transformer (Nawrot, Tworkowski, et al. 2022)and MegaByte (L. Yu et al. 2023) use small byte-level models to compress raw inputs into subsampled sequences,which are then processed with a more powerful standard language model. However, simple pooling strategies such as compressing every k inputs are not data-dependent, and perform poorly on modalities with variable information rates such as language.

·SpaceByte (Slagle 2024) and Byte Latent Transformer (Pagnoni et al. 2024) introduce data-dependent chunking strategies such as delimiter- or entropy-based heuristics. These heuristics, however, rely on auxiliary external boundary predictors, and are therefore modality-specific and not fully end-to-end.

·Although jointly trainable boundary predictors are the ideal solution, they require optimizing discrete selection operations without supervision, which is fundamentally a challenging problem. Consequently, existing end-to-end approaches (Nawrot, Chorowski, et al. 2023) exhibit training instabilities that preclude scaling beyond small models or nesting multi-level hierarchies.

Fundamentally,creating a tokenizer-free architecture requires incorporating the data chunking process directly into the model, while overcoming challenges in efficiency,learnability, and stability at scale.

## Dynamic Chunking: End-to-end Sequence Modeling Without Tokenization

In this work, we introduce an end-to-end **hierarchical** **network** **(H-Net)** that compresses raw data through a recursive,data-dependent **dynamic** **chunking** **(DC)** process (Figure 1). H-Nets match the efficiency of tokenized pipelines while substantially improving modeling ability, by replacing handcrafted heuristics with content-aware and context-dependent segmentation learned from data.

**Hierarchical** **Processing.** The H-Net adopts the hierarchical architecture from prior work (Goel et al. 2022;Nawrot,Tworkowski, et al. 2022; Slagle 2024), resembling an autoregressive U-Net (Ronneberger, Fischer, and Brox 2015): (i) raw data is processed by a small **encoder** **network,** (ii) then downsampled and passed through a **main** **network** operating on compressed chunks, (iii) and finally upsampled before being passed through a **decoder** **network** operating on the original resolution. This modularity creates a natural processing hierarchy where outer stages capture fine-grained patterns while inner stages operate on coarse representations akin to traditional tokens. Crucially, while the main network contains the bulk of parameters and can be any standard architecture designed for operating on tokenized language-such as a Transformer (Vaswani et al. 2017) or state space model (SSM) (A. Gu and Dao 2023)-we show that the encoder and decoder networks are strongly improved by using SSMMs, which have an inductive bias for compression (A. Gu 2025).

**Dynamic** **Chunking.** H-Net's core is a novel dynamic chunking (DC) mechanism which interfaces between the main network and the encoder/decoder networks, learning how to segment data while using standard differentiable optimization.DC is composed of two complementary new techniques: (i) a **routing** **module** which predicts boundaries between adjacent elements through a similarity score (ii) and a **smoothing** **module** which interpolates representations using the router's outputs, attenuating the effect of uncertain boundaries and significantly improving learnability. By combining these with a new auxiliary loss function that targets desired downsampling ratios, and modern techniques for gradient-based learning of discrete choices (Bengio, Léonard, and Courville 2013; Fedus, Zoph, and Shazeer 2022), DC lets an H-Net learn how to compress data in a fully end-to-end fashion.

**Signal** **Propagation.** We introduce several architectural and training techniques to improve stability and scalability during end-to-end optimization. These include: (i) carefully placing projections and normalization layers to balance signal propagation between interacting sub-networks, and (ii) adjusting optimization parameters for each layer based on its dimensionality and effective batch size, which changes between stages of the hierarchical structure.

Altogether, H-Net learns segmentation strategies optimized jointly with the main backbone, dynamically compressing input vectors based on contextual information into meaningful chunks. H-Net represents the first truly end-to-end, tokenizer-free

3Non-hierarchical models comprised of repeated blocks, such as the standard Transformer (Vaswani et al. 2017).

<!-- 2 -->

<!-- h e e n d 0 f t 0 k e n S ! C D Dechunking $\bar {Z}$ z (d) Upsampler $1-P_{t-2}$ $1-P_{t-1}$ $1-P_{t}$ $\bar {z}$ z $P_{t-3}$ $P_{t-2}$ $P_{t-1}$ $P_{t}$ $\hat {z}$ 2 M $M$ (c) Smoothing Module $x^{s+1}$ $\hat {x}^{s}$ $b^{s}$ (b) Downsampler p Chunking x (a) Routing Module ε T h e e n d 0 f t 0 k e n S Dynamic Chunking -->

Figure 1: (left) Architectural overview of H-Net with a two-stage hierarchical design $(S=2)$ .(right) Dynamic Chunking (DC). **(bottom-right** ) Key components of a chunking layer: (a) a routing module module for dynamically drawing chunk boundaries, and (b) a downsampler that selectively retains vectors based on boundary indicators, reducing sequence length while preserving semantically significant positions. **(top-right)** Key components of a dechunking layer: (c) a smoothing module for converting discrete chunks into interpolated representations, and (d) an upsampler that restores compressed vectors to their original resolution based on boundary indicators. Linear in Equation (3) and STE in Equation (9) are omitted in the illustration for brevity.

language model: with a single stage of dynamic chunking, a byte-level H-Net **matches** **the** **perplexity** **and** **downstream** **performance** **of** **a** **strong** **BPE-tokenized** **Transformer** at sizes exceeding 1B parameters. Empirically, the dynamic chunking module naturally compresses data to a similar resolution as BPE tokenizers (4.5-5 bytes/chunk) and qualitatively learns meaningful boundaries, all without any external supervision or heuristics.

<!-- 3 -->

<!-- **Hierarchical Chunking: From Data to Abstractions** -->

Beyond addressing tokenization, H-Net improves general sequence modeling across a wide range of settings. Subword tokenization in language models is a special case of chunking-the process of building higher-level abstractions from low-level data-and is a central component of intelligence.4 Crucially, because H-Net is fully end-to-end, it **can** **be** **iterated** **recursively**: **the** **main** **network** **can** **itself** **be** **an** **H-Net.** Intuitively, more stages of chuinking represent higher order meanings; just as characters can be combined into words, words can be combined into clauses, sentences, and beyond.Iterating the hierarchy should therefore lead to even more efficient use of compute and parameters, and more effective reasoning over compressed representations.

Recursive H-Nets represent a new class of foundation model architectures that not only overcome tokenization, but discover and operate over abstractions learned from raw data, leading to higher-quality models with less pre-processing. Iterating the 1-stage H-Net to 2 hierarchical stages further improves its capabilities and stronglyoutperforms all baselines, with steeper training curves and better scaling with data. A byte-level 2-stage H-Net overtakes the perplexity of a strong tokenized Transformer after just 30B training bytes, with the gap widening throughout training, and matches the downstream evaluations of the tokenized Transformer of twice its size.

Finally, H-Nets realize the benefits of overcoming tokenization:

·Robustness: Without special data mixes, the pretrained H-Net is dramatically more robust to textual perturbations than the token-based Transformer, as evaluated on the noisy HellaSwag suite of benchmarks.

·Interpretability: Qualitative visualizations of learned boundaries reveal that H-Net automatically discovers seman-tically coherent units without explicit supervision, validating that end-to-end learning successfully detects the structural patterns traditionally imposed through handcrafted tokenization.

·Other languages: H-Net's improvements are even more pronounced on languages without obvious segmentation cues,including Chinese and code (59.9 → 66.3 on XWinograd-zh compared totokenized Transformer) and DNA language modeling (3.6x improved data efficiency compared to isotropic models).

We publicly release model code5 and pretrained checkpoints⁶.

### 2 H-Net Architecture

H-Nets are defined as hierarchical U-Net-like networks, but with data-dependent dynamic subsampling that is learned end-to-end together with the rest of the model. We first introduce H-Net's hierarchical architecture for multi-level processing, establishing key design principles (Section 2.1). We then present our dynamic chunking mechanism that learns content-aware compression through standard optimization (Section 2.2). Next, we detail architectural and optimization enhancements specifically tailored for hierarchical sequence modeling (Section 2.3). Finally, we explain how H-Net preserves autoregressive properties throughout its hierarchical structure during both training and inference (Section 2.4).

#### 2.1 Architectural Overview

##### 2.1.1 Components of H-Net

H-Net employs a hierarchical architecture comprising three primary components - encoder networks $(ε)$ , main network $(M)$ , and decoder networks ( $D$ ) - where each component is implemented with a stack of sequence mixing layers(e.g.,Transformers or state space models (SSM)). In its simplest form, a single-stage H-Net consists of one encoder network,one main network, and one decoder network. Crucially, the architecture's key characteristic lies in the main network's unique property: it can itself be instantiated as a complete H-Net, enabling recursive construction of multi-level hierarchies.

This recursive design allows H-Net to scale to arbitrary depths. In an $S-stage$  model, we denote components at each stage using superscripts: encoder networks as $\mathcal {E}^{}$ and decoder networks as $\mathcal {D}^{}$ for stages $0\leq <S$ ,with the main network $M$ residing only at the final stage $s=S$ . For example, a two-stage model contains $\mathcal {E}^{0},\mathcal {E}^{1},\mathcal {M},\mathcal {D}^{1}$ and $\mathcal {D}^{0}$ as illustrated

4Chunking is a formal concept from cognitive psychology central to human memory and cognition, and is the inspiration for this work's terminology.

5https://github.com/goombalab/hnet

6https://huggingface.co/cartesia-ai

<!-- 4 -->

in Figure 1-(Left). Throughout this paper, we use superscripts to denote stage indices, though we omit them when all variables within an equation belong to the same stage.

Drawing inspiration from the U-Net architecture (Ronneberger, Fischer, and Brox 2015), H-Net progressively compresses input sequences into fewer vectors with richer semantic embeddings through a chunking layer, processes these representa-tions in the main network, then decompresses the sequence back to its original resolution using a dechunking layer. Unlike traditional U-Net designs, however, H-Net dynamically determines chunking boundaries rather than using fixed-size pooling operations. The overall pipeline can be formalized as:

$$\hat {x}^{s}=\mathcal {E}^{s}\left(x^{s}\right),\quad \hat {z}^{S}=\mathcal {M}\left(x^{S}\right),\quad \hat {z}^{s}=\mathcal {D}^{s}\left(z^{s}\right),\tag{1}$$

where the chunking layer and the dechunking layer operations are defined as:

$$\left(x^{s+1},p^{s}\right)=\text {Chunk}\left(\hat {x}^{s}\right),\tag{2}\quad z^{s}=\text {Dechunk}\left(\hat {z}^{s+1},p^{s}\right)+\text {Linear}\left(\hat {x}^{s}\right)\tag{3}$$

The initial input to the model $x^{0}\in \mathbb {R}^{L^{0}xD^{0}}$ where $L^{0}$  the input sequence length and $D^{0}$ isthe embedding dimension.Intuitively, $p^{s}\in [0,1]^{L^{s}}$ represents the chunking router's confidence that the token should be passed into the main stage.7This value is essential for both the chunk (Section 2.2.1) and dechunk operations (Section 2.2.2).

##### 2.1.2 Design Principles

**Encoder** **and** **Decoder** **Networks.** The encoder and decoder networks in H-Net face unique design constraints due to their dual objectives and computational requirements. Each encoder must simultaneously (i) preserve fine-grained information for transmission to its corresponding decoder through residual connections (3), and (ii) compress inputs into chunks of richer representations for the main network. The decoder, in turn, must effectively combine coarse-grained representations from the main network with fine-grained details from the encoder residuals.

Importantly, both encoders and decoders operate on uncompressed sequences, making computational efficiency a significant design constraint that shapes our architectural choices. Recent studies demonstrate that State Space Models (SSMs) (A. Gu and Dao 2023; A. Gu, Goel, Gupta, et al. 2022; A. Gu, Goel, and Ré 2022) excel at processing fine-grained data including audio (Goel et al. 2022), DNA sequences (Schiff et al. 2024), and robotic control signals (Lu et al. 2023).

Based on these insights, we employ Mamba-2 layers (Dao and A. Gu 2024) as the primary building blocks for the encoder and decoder networks. This choice yields two significant benefits: effective handling of fine-grained inputs, and substantially improved efficiency when processing long, uncompressed sequences. Our ablation studies (Section 3.3) confirm that SSM-based encoders/decoders significantly outperform Transformer layers, not just at the byte level but even on coarser inputs,which we attribute to their stronger inductive bias for compression which helps build abstractions (A. Gu 2025).

**Main** **Network.** H-Net's computational efficiency stems from strategic parameter allocation. We concentrate the majority of model capacity in the main network, which operates on progressively compressed sequences. After S stages of compression, the main network receives sequences where $L^{}«L^{0}$ , enabling much larger networks within the same computational budget. This design reflects two key principles: (i) compressed sequences allow more parameters and compute per chunk, and (ii) higher-level abstractions benefit from increased processing power.

The main network functions as a standard language model and can employ any sequence mixing architecture. We default to Transformer layers for two reasons: compressed representations align well with Transformers' strengths in processing discrete, semantically-rich tokens, and this choice enables more controlled comparison with traditional BPE-based Transformer baselines in our experiments. However, the modular design also allows straightforward substitution with alternative architectures (e.g., a state space model, hybrid, or H-Net itself) as explored in our ablations.

**Architectural** **Guidelines.** Compared to standard isotropic models, the H-Net's structure introduces several new dimensions of architectural parameters to balance the parameter/compute allocation to each network. To simplify the search space, we follow a few general guidelines.

⁷We also sometimes refer to it as a probability-it is interpreted as such in Appendix F-although we do not use it as a formal probability.

<!-- 5 -->

·First, we ensure the model width (often referred to as $d_{\text {model}}$  for isotropic architectures) is monotone in the hierarchy: $D^{0}\leq D^{1}\leq \cdots \leq D^{S}$ . This allows increasing compute and parameters used in the main network without significantly increasing its depth.

·Second, using efficient and powerful SSM layers in the outer networks allow reducing the number of layers used compared to similar prior architectures that only used Transformer layers (Slagle 2024); in this paper, we always stick to four layers (or the equivalent of four Mamba layers) in each encoder/decoder network.

To handle the changes in dimensions without an additional linear layer, we adopt the technique used in SpaceByte (Slagle 2024)with the marginal change: to expand dimensions (i.e., $\left.D^{}\rightarrow D^{+1}\right)$ we append all vectors with a shared trainable vector of dimension $D^{+1}-D^{}$ ;to reduce dimensions $(i..,$ $\left.D^{+1}\rightarrow D^{}\right)$ ,we take the first $D^{s}$ dimensions from each vector.

We note that H-Net's performance can likely be improved with more careful tuning of the layer allocation and hyperpa-rameters between sub-networks.

#### 2.2 Dynamic Chunking (DC)

H-Net learns chunking boundaries through end-to-end training, allowing it to identify semantically meaningful units adaptively. Furthermore, this dynamic approach enables the model to allocate computational resources efficiently by compressing low-information regions while preserving high-information content at appropriate granularity.

##### 2.2.1 Chunking Layer

The chunking layer (Chunk in equation (2)) contains a routing module and downsampler, as illustrated in Figure 1-(bottom-right).

**Routing** **Module.** In natural data, meaningful boundaries tend to emerge at points of contextual or semantic shift. From this observation, we add an inductive bias by measuring the similarity between adjacent representations: when context changes, consecutive vectors should exhibit lower similarity. The routing module implements this intuition through cosine similarity between adjacent encoder outputs. Given encoder outputs $+$ it calculates boundary probabilities $p_{t}$  and boundary indicators $b_{t}$  as follows:

$q_{t}=W_{q}\hat {x}_{t},$  $k_{t}=W_{k}\hat {x}_{t},$  $p_{t}=\frac {1}{2}\left(1-\frac {q_{t}^{\top }k_{t-1}}{\left\|q_{t}\right\|\left\|k_{t-1}\right\|}\right)\in [0,$  2 $b_{t}=\mathbb {1}_{\{p_{t}\geq 0.5\}},$  (4)

where $p_{1}=1.0$ by definition, ensuring the sequence begins with a boundary. This formulation scales cosine similarity into a boundary score or probability: when consecutive vectors $\hat {x}_{t-1}$  and $\hat {x}_{t}$  span a semantic boundary ( $e.g.$  , between morphemes,words, or phrases), their projections $q_{t}$  and $k_{t-1}$  diverge in the latent space, yielding low cosine similarity and consequently high boundary probability $p_{t}$ 

**Downsampler.** The downsampler compresses encoder outputs $\hat {x}^{s}$ into a reduced set of vectors $x^{s+1}$ using boundary indicators $\left\{b_{}^{}\right\}_{=1}^{L^{}}$ .Among potential compression strategies - including mean pooling, max pooling, or cross-attention-we adopt direct selection of boundary-marked vectors for its simplicity and effectiveness (see Appendix E.1 for ablations).

As illustrated in Figure 1-(b), this approach follows a straightforward selection rule: vectors where $b_{t}=1$ are retained in the compressed sequence $x^{+1}$ ,while those where $b_{t}=0$ are discarded. Likewise, the same downsampler applies to boundary probabilities,compressing $p^{s}$ into $P^{s+1}$ for use in a dechunking layer (see Section 2.2.2).

##### 2.2.2 Dechunking

The dechunking layer (Dechunk in equation (3)) consists of a smoothing module and upsampler, as illustrated in Figure 1-(top-right).

**Smoothing** **Module.** The critical challenge in training a dynamic chunking module lies in the discrete nature of chunk boundaries, which impedes gradient flow during backpropagation. We introduce the smoothing module as an elegant solution to this problem. As illustrated in Figure 1-(c), this component transforms discrete chunking operations into

<!-- 6 -->

<!-- ..new _product! $\hat {Z}_{\mathrm {ne}}\rightarrow \bar {Z}_{\text {_ne}}$ $\hat {Z}_{\text {ne}}$ $20$ $\hat {Z}_{\text {pro}}$ $\hat {Z}_{\mathrm {du}}\rightarrow \bar {Z}_{\text {produ}}$ 20 $\hat {Z}_{\text {duct!}}$ Upper Stages $x_{\mathrm {ne}}$ $x_{\text {new}_{-}}$ $x_{\text {pro}}$ $x_{\mathrm {du}}$ $x_{\text {duct!}}$ .. ne W prod uct ! -->

<!-- -new_product! $\hat {Z}_{\text {new_}}$ $\hat {Z}_{\text {pro}}$ 20 $\hat {Z}_{\text {ductl}}$ Upper Stages $x_{\text {new}_{-}}$ $x_{\text {pro}}$ $x_{\text {duct}}$ ...new product! · -->

<!-- ..new_product ! $\hat {Z}_{\mathrm {ne}}$ $\hat {Z}_{\text {new}_{-}}$ $\hat {Z}_{\text {pro}}$ $\hat {Z}_{\mathrm {du}}$ 2 $\hat {Z}_{\text {duct}}$ ! Upper Stages $x_{\mathrm {ne}}$ $x_{\text {new_}}$ $x_{\text {pro}}$ $x_{\mathrm {du}}$ $x_{\text {duct}}$ n e W product! -->

(a) Oracle Boundaries (b) w/o Smoothing Module (c) w/ Smoothing Module

Figure 2: Comparison of decompression strategies on the example sequence "...new product!". ·indicates a boundary with high confidence $\left(P_{t}=1.0\right)$ and indicates a boundary with low confidence $\left(P_{t}=0.5\right).$ .As each letter in the example is unique, we use the letters in subscripts to denote expected semantics of chunks. (a) Optimal chunking with oracle boundaries identifying linguistically meaningful units. (b) Suboptimal chunking without a smoothing module. This creates misalignment during upsampling, causing information from incorrect contexts to propagate. (c) Improved decompression with a smoothing module, where low-confidence chunks are interpolated with weighted combinations of previous chunks,correcting the shaded regions. In panels (b) and (c), we interpret low-confidence boundaries cause the encoder network to embed broader contexts at subsequent positions. Specifically, the vectors at _and ! encode new_and duct!, respectively (instead of $W_{-}$ and ct!).

differentiable computations by creating smooth interpolations between chunks. Concretely, the smoothing module applies an exponential moving average (EMA) with the following definition:

$$\bar {z}_{t}=P_{t}\hat {z}_{t}+\left(1-P_{t}\right)\bar {z}_{t-1}.\tag{5}$$

Our smoothing module performs several roles:

**·Differentiable** **Boundary** **Learning**: It transforms the discrete upsampling operation into a continuous one,enabling effective backpropagation through chunk boundaries during training without requiring stochastic exploration-based approaches (Jang, S. Gu, and Poole 2016).

**·Adaptive** **Error** **Correction**: Chunks with high confidence $\left(P_{t}\approx 1.0\right)$ maintain discrete boundaries $\left(\bar {z}_{t}\approx z_{t}\right),$ while chunks with low confidence $\left(P_{t}\approx 0.5\right)$ are smoothed using information from previous chunks, creating a self-correcting mechanism.

**·Training** **Stability**: By smoothly interpolating between discrete choices based on confidence scores, a smoothing module prevents the model from overfitting to suboptimal chunking patterns early in training.

Figure 2 illustrates this with the example "...new product!". The word "product" can be morphologically decomposed into "pro-" and $"-duct"8$ . Without the smoothing module (see Figure 2-(b)), suboptimal chunking(e.g., $u$ 'as shown with half-filled circles) creates alignment mismatches that disrupt information flow. With the smoothing module (see Figure 2-(c)), chunks with low confidence are smoothed with previous context, ensuring proper information propagation and enabling the model to learn optimal chunk boundaries through gradient descent.

**Upsampler.** We carefully design the upsampler (see Figure 1-(d)) that decompresses $\bar {z}^{s+1}$ to match the original resolution of inputs in the previous stage $z^{s}$ with the following definition:

$$c_{t}=p_{t}^{b_{t}}\left(1-p_{t}\right)^{1-b_{t}}=\left\{\begin{array}{ll}p_{t}&\text {if}b_{t}=1\\ 1-p_{t}&\text {otherwise}\end{array}\right.\tag{6}\quad \tilde {z}_{t}=\bar {z}_{\sum _{k=1}^{t}b_{k}},\tag{8}$$

$$\text {STE}\left(c_{t}\right)=c_{t}+\text {stopgradient}\left(1-c_{t}\right),\tag{7}\tag{9}\quad \text {Upsampler}(\bar {z},c)_{t}=\text {STE}\left(c_{t}\right)·\tilde {z}_{t}.$$

Each component serves a specific purpose in enabling stable end-to-end learning:

8pro--meaning forward or forth, -duct-from Latin ducere, meaning to lead or to bring

<!-- 7 -->

**·Confidence** **scoring** (6): The coefficient c quantifies the routing module's confidence in its boundary decisions. For positions marked as boundaries $\left(b_{t}=1\right),$ $c_{t}=p_{t}$ rewards high boundary probabilities. In contrast,for non-boundary positions $\left(b_{t}=0\right),c_{t}=1-p_{t}$ penalizes false boundary predictions. This formulation encourages the model to produce boundary probabilities near 1.0 at true boundaries and near 0.0 elsewhere.

**·Gradient** **stabilization** (7): The Straight-Through Estimator (STE) (Bengio, Léonard, and Courville 2013) is a well established technique from discrete representation learning (Jang, S. Gu, and Poole 2016; Van Den Oord, Vinyals,et al. 2017) that rounds confidence scores to 1.0 in the forward pass while maintaining continuous gradients during backpropagation. While H-Net already demonstrates strong performance without STE, incorporating this technique provides an additional performance boost that empirically further stabilizes the optimization dynamics.

**·Causal** **expansion** (8): The upsampling operation repeats each compressed vector until the next boundary position,ensuring that each reconstructed position receives information from its most recent chunk. This maintains the sequential flow of information while expanding the compressed representation back to its original length.

**·Confidence-weighted** **decompression** (9): Multiplying upsampled vectors by their confidence scores incentivizes the routing module to make confident, accurate decisions. High-confidence boundaries create direct reward signals that encourage the model to sharpen its boundary predictions through gradient feedback.

#### 2.3 Improved Techniques for Hierarchical Sequence Modeling

We introduce several techniques that improve the overall architecture. These may generally be considered techniques to improve signal propagation throughout the network, improving stability and learnability.

##### 2.3.1 Model Design Improvements

**Norm** **Balance.** Modern large language models employ pre-normalization architectures (Radford et al. 2019; Touvron,Lavril, et al. 2023), departing from the post-normalization design of the original Transformer (Vaswani et al. 2017). Following established best practices, these models typically include a final normalization layer after all residual blocks. H-Net adopts this convention through network normalization, by placing an RMSNorm (B. Zhang and Sennrich 2019) at the end of each network component( $\mathcal {E}^{s}$ , . $\mathcal {D}^{s},$ and $\mathcal {M}$ 

This addition of a normalization layer addresses a critical challenge in hierarchical architectures. Pre-normalization allows residual stream magnitudes to grow unbounded through successive layers, with feature norms increasing monotonically.For H-Net, this poses a particular problem: the architecture leverages residual connections to preserve fine-grained information across stages. Without network normalization, outputs from deeper components (especially the many-layered main network) would dominate the residual signals from earlier encoder networks through imbalanced feature norms,neglecting the fine-grained details that are essential for decompression. The normalization layers restore balance between processed features and residual information, ensuring both contribute meaningfully to the final representation.

**Separation** **of Two** **Streams.** Encoder outputs (x) serve dual purposes in our architecture: passing fine-grained infor-mation to corresponding decoders through residual connections, and providing compressed representations as inputs to subsequent stages. This dual functionality creates a design challenge, as these two roles may benefit from different representations. We consider three options to address this: (i) apply a projection to the residual connection only, (ii) apply a projection to the main network inputs only, (iii) and apply a projection to both pathways.

As indicated in equation (3), we adopt the first approach - adding a projection (Linear) only to the residual connection.This choice is motivated by the fundamental principle of designing deep learning models (K. He et al. 2016):maintaining intact gradient flow through the main computational path is crucial for effective training.

Empirically, we found that the third option underperforms despite additional parameters and computations, as the extra projections interfere with gradient propagation. The second option, while preserving residual gradients, disrupts the main network's gradient flow and had worse training dynamics. Our chosen design maintains unimpeded gradients from deeper stages while allowing the residual connection to adapt its contribution through the learned projection. This encourages the model to leverage the main network's computational depth while using residuals in a complementary role.

One additional detail is that this residual connection is initialized close to 0; earlier versions of H-Net found this to be an important detail, but it may be less important when combined with additional techniques such as LR modulation (Section 2.3.2).

<!-- 8 -->

##### 2.3.2 Optimization Improvements

**Ratio** **Loss** **for** **Controlled** **Compression.** Without explicit regularization, the model may converge to trivial solutions:either retaining nearly all vectors (negating computational benefits) or compressing excessively (losing critical information).Inspired by load balancing mechanisms in Mixture-of-Experts (MoE) models (Fedus, Zoph, and Shazeer 2022),which face similar challenges in maintaining balanced expert utilization, we introduce a ratio loss to guide compression:

$$\mathcal {L}_{\text {ratio}}=\frac {N}{N-1}((N-1)FG+(1-F)(1-G)),\quad F=\frac {1}{L}\sum _{t=1}^{L}b_{t},\quad G=\frac {1}{L}\sum _{t=1}^{L}p_{t},\tag{10}$$

where F represents the fraction of vectors actually selected, G denotes the average boundary probability, and NV controls the target compression ratio. Mechanistically, although F is not differentiable, the network can be trained toward targeted compression ratios through $G$ , which provides continuous feedback.

When $F=G,$  the loss attains a minimum of $\mathcal {L}_{\text {rtio}}=1$ when $F=G=\frac {1}{N}$ .Interestingly, the loss can theoretically fall below 1when $F\neq G\left(\text {e.g.,}F=\frac {1}{N}+ε\text {and}G=\frac {1}{N}-ε\right),$ which we indeed observe during training. Despite this theoretical possibility,the loss effectively guides the model toward the desired compression ratio in practice. In practice, as our architectural design encourages the routing module to make confident decisions (i.e., boundary probabilities approaching 0 or 1), F naturally converges toward G, and the loss effectively guides the model toward the desired compression ratio.

Combined together with the auto-regressive prediction loss $\left(\text {i..,}\mathcal {L}=\mathcal {L}_{\mathrm {AR}}+α\sum _{s=0}^{S-1}\mathcal {L}_{\text {atio}}^{s}\right)$ ,this mechanism preserves content-adaptive compression: the model learns which vectors to retain based on semantic importance rather than following predetermined patterns, distinguishing H-Net from fixed compression schemes. We fixed $α=0$ .03in all experiments in this paper as it provides a good balance between prediction accuracy and chunking efficiency; however, in other settings, it may be important to choose this hyperparameter more carefully.

Notationally, we sometimes use $\left(N^{0}N^{1}\cdots N^{}\right)-\mathrm {DC}$ to denote the full dynamic chunking mechanism together with its targeted chunking ratios.

**Learning** **Rate** **Modulation** The hierarchical design of H-Net requires careful adjustment of learning rates across stages to ensure balanced training dynamics. Modern theory establishes that neural network hyperparameters should be scaled in predictable ways for optimal trainability (G. Yang and E. J. Hu 2020). Concretely, outer stages, which handle significantly longer input sequences, receive proportionally higher learning rates than inner stages operating on compressed representations. This scaling follows established principles that learning rates are adjusted based on effective batch size and model dimensions. The specific scaling factor we use accounts for both the total number of inputs processed at each stage and the corresponding hidden dimensions (see Appendix C). With this modulation, the model achieves more stable training dynamics and improved convergence behavior across the entire hierarchy. In particular, we empirically find that since outer stages directly influence the chunk boundaries that inner stages depend on, the higher learning rates in the outer stages seem to accelerate learning the chunking mechanism.

#### 2.4 Autoregressive Training and Inference

Every component of H-Net (i.e., encoder-, decoder-, main-networks, and the dynamic chunking mechanism) is carefully designed to preserve autoregressive properties essential for language modeling.

**Training.** During training, H-Net employs standard causal masking across all sequence mixing layers. DC maintains causality by computing boundary probabilities based only on current and previous representations. Specifically,the boundary probability $p_{t}$  depends on $q_{t}$  and $k_{t}$  from the current and previous positions (equation (4)),ensuring no information leakage from future tokens. The smoothing module similarly maintains causality through its recursive formulation (equation (5)), where each output depends only on past compressed representations.

**Inference.** For inference, H-Net generates raw bytes (or whatever the outermost modality is) autoregressively with a modified proceduure to handle its hierarchical structure.

Generation with a prompt proceeds as follows:

<!-- 9 -->

1. **Initial** **processing**: During prefill, we generate chunks via the encoders (as in training). For each component (i.e.the isotropic components, and the routing module and dechunking layer), we generate a state. Isotropic state (e.g.KV cache for Transformer layers, SSM state for Mamba-2 layers) is generated as usual.

2.DC **state** **and** **DC** **step:** As noted above, the DC modules have recursive formulations that maintain causality at train-time.These recursive formulations become autoregressive formulations at inference time.

(a) **Routing** **Module:** In order to compute $p_{t}$ , we need $k_{t-1}$  (see equation (4)), so our state consists of the key value of the most recent token processed.

(b) **Dechunking** **Layer**: In order to compute $\tilde {z}_{t}$ ,we need $P_{t}$ and $\tilde {z}_{t-1}$ . Thus, the dechunking layer state should consist of the last z value.

3. **Token** **Generation**: To perform a model step, we do the following for a 1-stage hierarchy:

(a) Pass the token through the encoder network,

(b) Step the routing module to determine whether the token needs to be processed by the main network,

(c) Step the main network if necessary, in which case we also need to step the dechunking layer.

(d) Use the result of the dechunking layer to step the decoder network.

A consequence of this inference formulation is that, at inference time, H-Net decides individually for each token how much compute to use when processing it. Therefore, H-Net can allocate more or less compute to different tokens as it deems necessary. A particular connection is that inference resembles specuulative decoding (Leviathan, Kalman, and Matias 2023), which also involves a small network (the draft model) stepping on every token, and a larger network (the verification model) only stepping on contiguous chunks of every few tokens.

### 3 Experiments

We first describe our general experimental protocol for language modeling, used for the majority of our experiments.In Section 3.1, we evaluate on a high-quality English dataset, showing much significantly stronger performance than baselines,as well as improved robustness and interpretability from avoiding tokenization. In Section 3.2, we extend our evaluation to diverse datasets including Chinese, code, and DNA, with even larger performance improvements, demonstrating H-Net's versatility as a general sequence model architecture. In Section 3.3, we provide comprehensive ablations that study individual architectural components and design choices.

**Models.** We compare against a standard tokenized Transformer following the Llama architecture (Grattafiori et al. 2024;Touvron, Martin, et al. 2023).10 We additionally compare against several byte-level baselines:

·MambaByte (J. Wang et al. 2024) is an isotropic model using pure Mamba-2 layers.

·LlamaByte is an isotropic model using pure Transformer layers.

·SpaceByte (Slagle 2024) represents the canonical hierarchical architecture with external boundary supervision, which chunks on spaces and "space-like" bytes.1 On English, the space-like delimiter heuristic empirically has an average ratio of 6.0 bytes per chunk.

·SpaceByt $e++$ is our modification of SpaceByte that includes our architectural modifications to the hierarchical structure (from Section 2.1). In particular, it changes the outer encoder/decoder networks to use Mamba-2, and modifies the layer counts and widths slightly to match the H-Net models below.

·H-Net (space) further improves SpaceByte $e++$ with our training improvements to the network (Section 2.3), in particular, adding post-network norms, residual projections, and learning rate multipliers on the outer networks.H-Net (space) differs from our full H-Net only through the chunking function.

'Here, we use token in the autoregressive generation sense, referring to one time step, not in the literal BPE token sense.

10This was called the "Transformer++" in A. Gu and Dao (2023); since by now it is firmly established, we remove the "++".

11BLT is another architecture with external supervision using entropy instead of delimiters, but is unfortunately too complex to set up and control as a baseline. We believe that the delimiter-based method is highly competitive. See Appendix A.1.3.

<!-- 10 -->

Table 1**:Architectures for main language models, all data-/FLOP-matched.** $\mathcal {E}^{0},\mathcal {D}^{0}$  $\mathcal {E}^{1},\mathcal {D}^{1}$ , $M$ .Tand M denote a Transformer and a Mamba-2 layer, respectively. For hierarchical byte-level models, the TOKENIZER column lists the chunking mechanism. The numbers before DC indicate downsampling factor N in equation (10); for example,(3,3)-DC denotes $^{0}=^{1}=3$ .The BPIC (Bytes-Per-Innermost-Chunk) measure shows that each chunk dynamically determined by our 1-stage comprises similar number of bytes to the GPT-2 tokenizer, despite aiming for $N^{0}=6$ AllI Transformer layers in ε or $D$  networks, as well as LlamaByte, use Sliding Window Attention (SWA) with a window size of 1024.

<table border="1" ><tr>
<td colspan="1" rowspan="1">MODEL </td>
<td colspan="1" rowspan="1">INPUT </td>
<td colspan="1" rowspan="1">TOKENIZER </td>
<td colspan="1" rowspan="1">$L^{0}$</td>
<td colspan="1" rowspan="1">BPIC<br>$\left(L^{S}/L^{0}\right)$</td>
<td colspan="1" rowspan="1">#PARAMS</td>
<td colspan="1" rowspan="1">ARCHITECTURE </td>
<td colspan="1" rowspan="1">d_model(D)</td>
</tr><tr>
<td colspan="8" rowspan="1">#FLOPs matched to GPT-3 Large </td>
</tr><tr>
<td colspan="1" rowspan="1">Transformer </td>
<td colspan="1" rowspan="1">Token </td>
<td colspan="1" rowspan="1">GPT2 </td>
<td colspan="1" rowspan="1">1792 </td>
<td colspan="1" rowspan="1">4.6 </td>
<td colspan="1" rowspan="1">760M </td>
<td colspan="1" rowspan="1">T24 </td>
<td colspan="1" rowspan="1">1536 </td>
</tr><tr>
<td colspan="1" rowspan="1">LlamaByte </td>
<td colspan="1" rowspan="7">Byte </td>
<td colspan="1" rowspan="1">-</td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">1.0 </td>
<td colspan="1" rowspan="1">210M </td>
<td colspan="1" rowspan="1">T16 </td>
<td colspan="1" rowspan="1">1024 </td>
</tr><tr>
<td colspan="1" rowspan="1">MambaByte </td>
<td colspan="1" rowspan="1">-</td>
<td colspan="1" rowspan="2"></td>
<td colspan="1" rowspan="1">1.0 </td>
<td colspan="1" rowspan="1">190M </td>
<td colspan="1" rowspan="1">M28 </td>
<td colspan="1" rowspan="1">1024 </td>
</tr><tr>
<td colspan="1" rowspan="1">SpaceByte </td>
<td colspan="1" rowspan="1">Spacelike </td>
<td colspan="1" rowspan="1">6.0 </td>
<td colspan="1" rowspan="1">570M </td>
<td colspan="1" rowspan="1">$\mathrm {T}8+\mathrm {T}16+\mathrm {T}8$</td>
<td colspan="1" rowspan="1">768,1536 </td>
</tr><tr>
<td colspan="1" rowspan="1">SpaceByte++</td>
<td colspan="1" rowspan="1">Spacelike </td>
<td colspan="1" rowspan="2">8192 </td>
<td colspan="1" rowspan="1">6.0 </td>
<td colspan="1" rowspan="1">850M </td>
<td colspan="1" rowspan="1">$\mathrm {M}4+\mathrm {T}28+\mathrm {M}4$</td>
<td colspan="1" rowspan="1">1024,1536 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(pool) </td>
<td colspan="1" rowspan="1">6-Pool </td>
<td colspan="1" rowspan="1">6.0 </td>
<td colspan="1" rowspan="1">850M </td>
<td colspan="1" rowspan="1">$\mathrm {M}4+\mathrm {T}28+\mathrm {M}4$</td>
<td colspan="1" rowspan="1">1024,1536 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(space) </td>
<td colspan="1" rowspan="1">Spacelike </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">6.0 </td>
<td colspan="1" rowspan="1">850M </td>
<td colspan="1" rowspan="1">$\mathrm {M}4+\mathrm {T}28+\mathrm {M}4$</td>
<td colspan="1" rowspan="1">1024,1536 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(1-stage) </td>
<td colspan="1" rowspan="1">6-DC </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">4.8 </td>
<td colspan="1" rowspan="1">680M </td>
<td colspan="1" rowspan="1">$\mathrm {M}4+\mathrm {T}22+\mathrm {M}4$</td>
<td colspan="1" rowspan="1">1024,1536 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(2-stage) </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">(3,3)-DC </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">7.0 </td>
<td colspan="1" rowspan="1">870M </td>
<td colspan="1" rowspan="1">$\mathrm {M}4+\mathrm {T}1\mathrm {M}4+\mathrm {T}26+\mathrm {M}4\mathrm {\sim T}1+\mathrm {M}4$</td>
<td colspan="1" rowspan="1">1024,1024,1536 </td>
</tr><tr>
<td colspan="8" rowspan="1">#FLOPs matched to GPT-3 XL </td>
</tr><tr>
<td colspan="1" rowspan="1">Transformer </td>
<td colspan="1" rowspan="1">Token </td>
<td colspan="1" rowspan="1">GPT2 </td>
<td colspan="1" rowspan="1">1792 </td>
<td colspan="1" rowspan="1">4.6 </td>
<td colspan="1" rowspan="1">1.3B </td>
<td colspan="1" rowspan="1">T24 </td>
<td colspan="1" rowspan="1">2048 </td>
</tr><tr>
<td colspan="1" rowspan="1">SpaceByte++</td>
<td colspan="1" rowspan="4">Byte </td>
<td colspan="1" rowspan="1">Spacelike </td>
<td colspan="1" rowspan="4">8192 </td>
<td colspan="1" rowspan="1">6.0 </td>
<td colspan="1" rowspan="1">1.6B </td>
<td colspan="1" rowspan="1">$\mathrm {M}4+\mathrm {T}31+\mathrm {M}4$</td>
<td colspan="1" rowspan="1">1024,2048 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(space) </td>
<td colspan="1" rowspan="1">Spacelike </td>
<td colspan="1" rowspan="1">6.0 </td>
<td colspan="1" rowspan="1">1.6B </td>
<td colspan="1" rowspan="1">$\mathrm {M}4+\mathrm {T}31+\mathrm {M}4$</td>
<td colspan="1" rowspan="1">1024,2048 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(1-stage) </td>
<td colspan="1" rowspan="1">6-DC </td>
<td colspan="1" rowspan="1">4.7 </td>
<td colspan="1" rowspan="1">1.3B </td>
<td colspan="1" rowspan="1">$\mathrm {M}4+\mathrm {T}24+\mathrm {M}4$</td>
<td colspan="1" rowspan="1">1024,2048 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(2-stage) </td>
<td colspan="1" rowspan="1">(3,3)-DC </td>
<td colspan="1" rowspan="1">6.9 </td>
<td colspan="1" rowspan="1">1.6B </td>
<td colspan="1" rowspan="1">$\mathrm {M}4+\mathrm {T}1\mathrm {M}4+\mathrm {T}27+\mathrm {M}4\mathrm {\sim T}1+\mathrm {M}4$</td>
<td colspan="1" rowspan="1">1024,1536,2048 </td>
</tr></table>

·H-Net (pool) is a baseline ablating the effect of a simple chunking strategy that pools every $k$  tokens,which is expected to be weaker than all of the data-dependent chunking strategies.

·H-Net (1-stage) is our full HH-Net method with DC learned end-to-end (Section 2.2) with compression target $N^{0}=6.$ 

·H-Net (2-stage) is our full H-Net method, iterated to two nested stages using $N^{0}=3,N^{1}=3.$ 

We provide results for two model scales, Large (L) and XL. Each scale is FLOP-matched to the corresponding GPT-3 (T. Brown et al. 2020) (i.e., GPT-3 L and GPT-3 XL) variant of the tokenized Transformer (760M and 1.3B parameters respectively).

**Experimental** **Setup.** Following established practice (Slagle 2024; J. Wang et al. 2024; Xue et al. 2022), we measure performance using bits-per-byte (BPB) to ensure comparability across different input representations. For tokenized models,this amounts to simply rescaling the total negative log likelihood of a sequence (in tokens) by the total number ofbytes.In addition, we systematically control the data and compute budget for all models (see Table 1), matching all models carefully in both **bytes-per-batch and FLOPs-per-byte:**12

·Data Budget: We train all models on the 100B token subset sampled from the FineWeb-Edu dataset (Penedo et al.2024). All tokenizer-free models process 8192 utf-8 encoded bytes per sequence, while the Transformer uses 1792tokens from the GPT2 tokenizer (roughly equivalent to 8192 bytes). We use batch size 256 for all models; the total batch size is just under 0.5M tokens per batch for the baseline BPE Transformer, roughly matching protocols from prior work (A. Gu and Dao 2023).

·Compute Budget: For calculating FLOPs, we follow standard methodology (Hoffmann et al. 2022) with an extension for Mamba-2 layers (see Appendix B). We use the BPE-tokenized Transformer's #FLOPs as a reference, and the number of layers of the other models is adjusted accordingly to match the reference #FLOPs.

12 Another way to interpret this is that every model sees the exact same underlying data (regardless of tokenization) per minibatch, and every model aims to use the same number of FLOPs in every forward/backward pass.

<!-- 11 -->

Training employs AdamW (Loshchilov and Hutter 2017) optimizer with warmup-stable-decay (WSD) (S. Hu et al. 2024)scheduling with 10% linear warmup and 20% inverse-square-root decay (Ibrahim et al. 2024). Following Hägele et al. (2024)which recommends WSD schedulers with half the maximum learning rates as a cosine schedule, we adopt learning rates 2.5x higher than GPT-3 (Radford et al. 2019) standards; this corresponds to half of the maximum learning rate used in A. Gu and Dao (2023), yielding 6. $25\times 10^{-4}$ for Large-scale models and $5010^{-4}$ for XL-scale models. Architecture details include gated MLPs (Touvron, Martin, et al. 2023) in all Transformer layers and the main network's Mamba layers,while Mamba layers in ε and $\mathcal {D}$  are without an MLP.13 For Transformer layers in & and $D$ , we use Sliding Window Attention (SWA) (Beltagy, Peters, and Cohan 2020) with the windowv size of 1024. As discussed Section 2.1, ε and $D$  comprise mainly Mamba-2 layers.

#### 3.1 Language Modeling

**Training** **Curves.** Figure 3 presents validation BPB metrics throughout training for both Large and XL model scales.

**Large** **Scale.** At **the** Large scale, we make note of the following comparisons.

·All isotropic models severely underperform hierarchical models. Among these, **MambaByte** is significantly better than **LlamaByte,** both the FLOP-matched sliding window attention (SWA) variant and even the global attention variant that is data-matched but uses2x the FLOPs.

**·H-Net** **(pool)** is much worse than all other H-Net variants, validating that fixed-width chunking is not effective.

**·SpaceByte** is much worse than **SpaceByte++,** validating our strategy for network design as well as usage of Mamba in the outer networks (Section 2.1). **SpaceByte++**is in turn worse than **H-Net** **(space),** validating our improved signal propagation techniques (Section 2.3).

**·H-Net** **(space)** is a very strong model reaching the performance of the **BPE** **Transformer,** validating the effect of data-dependent chunking strategies together with a well-designed hierarchical architecture.

**·H-Net (1-stage)** is stronger than **H-Net** **(space),** validating that our dynamic chunking mechanism successfully learns how to segment data in a context-dependent way that improves over strong heuristics.

**·H-Net (2-stage)** is significantly better than **H-Net** **(1-stage),** validating that iterated dynamic chunking can potentially learn a nested hierarchy of useful features, and leverage compute and parameters even more effectively.

**XL** **Scale.** At the XL scale, we zoom in more closely and compare only the strongest set of methods:SpaceByte++,H-Net (space), H-Net (1-stage), and H-Net (2-stage).

The same trends hold as at the Large scale. Our SpaceByte++ baseline is strong, but slightly worse than the H-Net (space)model. **All byte-level H-Net methods start off worse than the Transformer, but scale better after enough data.**H-Net (space), H-Net (1-stage), and H-Net (2-stage) cross over the tokenized Transformer after just 200B bytes, 100B bytes,and 30B bytes respectively. Beyond these points, H-Net's performance advantage widens progressively, demonstrating that the benefits of learnable dynamic chunking get strengthened with additional training data, as the model continuously refines its chunking strategy.

**Downstream Evaluations.** Table 2 presents zero-shot accuracy across diverse downstream benchmarks (Bisk et al.2020; P. Clark et al. 2018; Mihaylov et al. 2018; Paperno et al. 2016; Sakaguchi et al. 2021; Zellers et al. 2019) using 1m-evaluation-harness (L. Gao,Tow, et al. 2024) for models at Large and XL scales. SpaceByte++,H-Net (space),and H-Net (1-stage) all have similar performance to the BPE Transformer at Large scale, and slightly outperform it at the XL scale, consistent with their close training curves (and possibly reflecting some noise in thme evaluations).

**H-Net (2-stage) consistently achieves the highest performance across most tasks,** outperforming 2.2% and 2.6%over the Transformer baseline at Large and XL scales respectively. Notably, the Large H-Net (2-stage) matches the average downstream performance of the XL BPE Transformer.

13Just as in the original Mamba (A. Gu and Dao 2023) and Mamba-2 (Dao and A. Gu 2024) blocks, our Mamba layers have roughly $6\left(D^{}\right)^{}$ parameters and Transformer layers have $12\left(D^{s}\right)^{2}$ parameters in stage s.

<!-- 12 -->

<!-- #FLOPs matched to GPT-3 Large (760M) #FLOPs matched to GPT-3 XL (1.3B) 1.05- 0.95- 1.00- 0.90- 0.95- aTo-ad-s1S UoIepIIeA 0.90- 0.85- 100B 0.85- 0.80- 200B 30B 0.80- 0.75- H-Net (2-stage) overtakes (BPE) Transformer with 30B training bytes 0.75- 50B 100B 508 100B $150B$ $200B$ 350B Total Training Bytes Total Training Bytes H-Net (2-stage) H-Net(space) SpaceByte++ LlamaByte (SWA) (BPE) Transformer H-Net (1-stage) H-Net (6-pool) SpaceByte LlamaByte (Global) MambaByte -->

Figure 3: **Validation** **Bits-per-byte** **(BPB)** **throughout training** for different models at Large (760M, left) and XL (1.3B,right) scales with matched computational and data budgets for training. All models but Transformer take raw byte inputs (Transformer uses GPT-2 tokenizer). Vertical dotted lines indicate crossover points where H-Net begins to ouitperform Transformer with predefined BPE tokenization. From the curves we can clearly see the following: (1) all hierarchical models (i.e., SpaceByte++, H-Net variants) outperform the isotropic models (i.e., Transformer, MambaByte,LlamaByte);(2)dynamic chunking is more powerful than BPE tokenizers; and (3) DC is more effective than other chunking strategies.Furthermore, H-Net's 2-stage variant consistently outperforms 1-stage across both scales, demonstrating the effectiveness of deeper hierarchies. See Table 1 for architectural details.

Table 2**: Zero-shot performance comparison across multiple benchmarks, all data-/FLOP-matched.Evaluation** results on seven downstream tasks at both Large (760M) and XL (1.3B) scales. GLOPs/BYTE is measured during evaluation on FineWeb-Edu validation set. See Table 1 for architectural details.

<table border="1" ><tr>
<td colspan="1" rowspan="1">MODEL </td>
<td colspan="1" rowspan="1">INPUT </td>
<td colspan="1" rowspan="1">GFLOPs/BYTE </td>
<td colspan="1" rowspan="1">F-EDU BPB↓</td>
<td colspan="1" rowspan="1">LMB. ACC↑</td>
<td colspan="1" rowspan="1">HELLA. ACC_n↑</td>
<td colspan="1" rowspan="1">PIQA ACC↑</td>
<td colspan="1" rowspan="1">ARC-E ACC↑</td>
<td colspan="1" rowspan="1">ARC-c<br>$ACC_n\uparrow$</td>
<td colspan="1" rowspan="1">WINO. ACC↑</td>
<td colspan="1" rowspan="1">OPEN.<br>$Acc_n1$/</td>
<td colspan="1" rowspan="1">AVERAGE ACC↑</td>
</tr><tr>
<td colspan="12" rowspan="1">#FLOPs matched to GPT-3 Large </td>
</tr><tr>
<td colspan="1" rowspan="1">Transformer </td>
<td colspan="1" rowspan="1">Token </td>
<td colspan="1" rowspan="1">0.42 </td>
<td colspan="1" rowspan="1">0.756 </td>
<td colspan="1" rowspan="1">45.0 </td>
<td colspan="1" rowspan="1">54.5 </td>
<td colspan="1" rowspan="1">72.3 </td>
<td colspan="1" rowspan="1">69.9 </td>
<td colspan="1" rowspan="1">36.3 </td>
<td colspan="1" rowspan="1">55.9 </td>
<td colspan="1" rowspan="1">38.8 </td>
<td colspan="1" rowspan="1">53.3 </td>
</tr><tr>
<td colspan="1" rowspan="1">LlamaByte </td>
<td colspan="1" rowspan="2"></td>
<td colspan="1" rowspan="1">0.42 </td>
<td colspan="1" rowspan="1">0.859 </td>
<td colspan="1" rowspan="1">37.0 </td>
<td colspan="1" rowspan="1">40.5 </td>
<td colspan="1" rowspan="1">64.7 </td>
<td colspan="1" rowspan="1">55.1 </td>
<td colspan="1" rowspan="1">26.7 </td>
<td colspan="1" rowspan="1">52.3 </td>
<td colspan="1" rowspan="1">32.4 </td>
<td colspan="1" rowspan="1">44.1 </td>
</tr><tr>
<td colspan="1" rowspan="1">LlamaByte(Global) </td>
<td colspan="1" rowspan="1">0.95 </td>
<td colspan="1" rowspan="1">0.845 </td>
<td colspan="1" rowspan="1">36.4 </td>
<td colspan="1" rowspan="1">41.5 </td>
<td colspan="1" rowspan="1">65.7 </td>
<td colspan="1" rowspan="1">57.2 </td>
<td colspan="1" rowspan="1">27.1 </td>
<td colspan="1" rowspan="1">49.8 </td>
<td colspan="1" rowspan="1">32.2 </td>
<td colspan="1" rowspan="1">44.3 </td>
</tr><tr>
<td colspan="1" rowspan="1">MambaByte </td>
<td colspan="1" rowspan="6">Byte </td>
<td colspan="1" rowspan="1">0.42 </td>
<td colspan="1" rowspan="1">0.845 </td>
<td colspan="1" rowspan="1">32.9 </td>
<td colspan="1" rowspan="1">42.0 </td>
<td colspan="1" rowspan="1">66.2 </td>
<td colspan="1" rowspan="1">55.9 </td>
<td colspan="1" rowspan="1">28.1 </td>
<td colspan="1" rowspan="1">51.7 </td>
<td colspan="1" rowspan="1">33.2 </td>
<td colspan="1" rowspan="1">44.3 </td>
</tr><tr>
<td colspan="1" rowspan="1">SpaceByte </td>
<td colspan="1" rowspan="1">0.41 </td>
<td colspan="1" rowspan="1">0.791 </td>
<td colspan="1" rowspan="1">43.0 </td>
<td colspan="1" rowspan="1">49.0 </td>
<td colspan="1" rowspan="1">69.0 </td>
<td colspan="1" rowspan="1">63.3 </td>
<td colspan="1" rowspan="1">33.5 </td>
<td colspan="1" rowspan="1">53.3 </td>
<td colspan="1" rowspan="1">35.0 </td>
<td colspan="1" rowspan="1">49.4 </td>
</tr><tr>
<td colspan="1" rowspan="1">SpaceByte++</td>
<td colspan="1" rowspan="1">0.42 </td>
<td colspan="1" rowspan="1">0.760 </td>
<td colspan="1" rowspan="1">48.0 </td>
<td colspan="1" rowspan="1">55.7 </td>
<td colspan="1" rowspan="1">71.3 </td>
<td colspan="1" rowspan="1">67.9 </td>
<td colspan="1" rowspan="1">35.4 </td>
<td colspan="1" rowspan="1">57.5 </td>
<td colspan="1" rowspan="1">39.6 </td>
<td colspan="1" rowspan="1">53.6 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(pool) </td>
<td colspan="1" rowspan="1">0.42 </td>
<td colspan="1" rowspan="1">0.780 </td>
<td colspan="1" rowspan="1">43.2 </td>
<td colspan="1" rowspan="1">54.7 </td>
<td colspan="1" rowspan="1">69.7 </td>
<td colspan="1" rowspan="1">67.9 </td>
<td colspan="1" rowspan="1">34.7 </td>
<td colspan="1" rowspan="1">54.8 </td>
<td colspan="1" rowspan="1">36.4 </td>
<td colspan="1" rowspan="1">51.6 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(space) </td>
<td colspan="1" rowspan="1">0.42 </td>
<td colspan="1" rowspan="1">0.755 </td>
<td colspan="1" rowspan="1">46.7 </td>
<td colspan="1" rowspan="1">55.9 </td>
<td colspan="1" rowspan="1">72.4 </td>
<td colspan="1" rowspan="1">68.8 </td>
<td colspan="1" rowspan="1">34.6 </td>
<td colspan="1" rowspan="1">57.6 </td>
<td colspan="1" rowspan="1">38.0 </td>
<td colspan="1" rowspan="1">53.4 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(1-stage) </td>
<td colspan="1" rowspan="1">0.43 </td>
<td colspan="1" rowspan="1">0.755 </td>
<td colspan="1" rowspan="1">46.2 </td>
<td colspan="1" rowspan="1">55.5 </td>
<td colspan="1" rowspan="1">71.0 </td>
<td colspan="1" rowspan="1">68.1 </td>
<td colspan="1" rowspan="1">35.6 </td>
<td colspan="1" rowspan="1">58.6 </td>
<td colspan="1" rowspan="1">40.0 </td>
<td colspan="1" rowspan="1">53.6 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(2-stage) </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">0.43 </td>
<td colspan="1" rowspan="1">0.743 </td>
<td colspan="1" rowspan="1">46.9 </td>
<td colspan="1" rowspan="1">57.4 </td>
<td colspan="1" rowspan="1">72.0 </td>
<td colspan="1" rowspan="1">71.7 </td>
<td colspan="1" rowspan="1">39.2 </td>
<td colspan="1" rowspan="1">60.4 </td>
<td colspan="1" rowspan="1">40.6 </td>
<td colspan="1" rowspan="1">55.5 </td>
</tr><tr>
<td colspan="12" rowspan="1">#FLOPs matched to GPT-3 XL </td>
</tr><tr>
<td colspan="1" rowspan="1">Transformer </td>
<td colspan="1" rowspan="1">Token </td>
<td colspan="1" rowspan="1">0.69 </td>
<td colspan="1" rowspan="1">0.730 </td>
<td colspan="1" rowspan="1">48.1 </td>
<td colspan="1" rowspan="1">58.0 </td>
<td colspan="1" rowspan="1">73.1 </td>
<td colspan="1" rowspan="1">72.2 </td>
<td colspan="1" rowspan="1">37.5 </td>
<td colspan="1" rowspan="1">58.6 </td>
<td colspan="1" rowspan="1">40.8 </td>
<td colspan="1" rowspan="1">55.5 </td>
</tr><tr>
<td colspan="1" rowspan="1">SpaceByte++</td>
<td colspan="1" rowspan="4">Byte </td>
<td colspan="1" rowspan="1">0.72 </td>
<td colspan="1" rowspan="1">0.733 </td>
<td colspan="1" rowspan="1">51.3 </td>
<td colspan="1" rowspan="1">60.1 </td>
<td colspan="1" rowspan="1">72.4 </td>
<td colspan="1" rowspan="1">71.8 </td>
<td colspan="1" rowspan="1">38.0 </td>
<td colspan="1" rowspan="1">58.5 </td>
<td colspan="1" rowspan="1">40.6 </td>
<td colspan="1" rowspan="1">56.1 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(space) </td>
<td colspan="1" rowspan="1">0.70 </td>
<td colspan="1" rowspan="1">0.726 </td>
<td colspan="1" rowspan="1">50.3 </td>
<td colspan="1" rowspan="1">61.5 </td>
<td colspan="1" rowspan="1">73.6 </td>
<td colspan="1" rowspan="1">72.4 </td>
<td colspan="1" rowspan="1">40.2 </td>
<td colspan="1" rowspan="1">60.2 </td>
<td colspan="1" rowspan="1">41.8 </td>
<td colspan="1" rowspan="1">57.1 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(1-stage) </td>
<td colspan="1" rowspan="1">0.72 </td>
<td colspan="1" rowspan="1">0.728 </td>
<td colspan="1" rowspan="1">48.4 </td>
<td colspan="1" rowspan="1">59.5 </td>
<td colspan="1" rowspan="1">72.4 </td>
<td colspan="1" rowspan="1">73.0 </td>
<td colspan="1" rowspan="1">38.3 </td>
<td colspan="1" rowspan="1">59.2 </td>
<td colspan="1" rowspan="1">42.4 </td>
<td colspan="1" rowspan="1">56.2 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(2-stage) </td>
<td colspan="1" rowspan="1">0.69 </td>
<td colspan="1" rowspan="1">0.715 </td>
<td colspan="1" rowspan="1">50.5 </td>
<td colspan="1" rowspan="1">62.2 </td>
<td colspan="1" rowspan="1">73.7 </td>
<td colspan="1" rowspan="1">74.2 </td>
<td colspan="1" rowspan="1">42.2 </td>
<td colspan="1" rowspan="1">60.5 </td>
<td colspan="1" rowspan="1">44.0 </td>
<td colspan="1" rowspan="1">58.2 </td>
</tr></table>

<!-- 13 -->

Table 3**: Robustness evaluation on HellaSwag with textual perturbations, all data-/FLOP-matched.Zero-shot** accuracy on five different perturbation types (AntSpeak, Drop, RandomCase, Repeat, UpperCase) for models trained exclusively on clean data without noise augmentation. Best and second best results in each column are denoted using bolded and underlined texts, respectively. The Robustness Score metric show that all byte-level models are more robust to adversarial text inputs than tokenizer-based Transformer. H-Net (2-stage) shows significantly enhanced robustness in textual perturbations, with the highest average accuracy across all noise types and highest robustness score. See Table 1for architectural details, and Appendix D.1 for the definition of Robustness Score.

<table border="1" ><tr>
<td colspan="1" rowspan="2">MODEL </td>
<td colspan="1" rowspan="2">INPUT<br>ANTSPEAK </td>
<td colspan="4" rowspan="1">HELLASWAG </td>
<td colspan="1" rowspan="2">AVERAGE↑</td>
<td colspan="1" rowspan="2">ROBUSTNESS SCORE↑</td>
</tr><tr>
<td colspan="1" rowspan="1">DROP </td>
<td colspan="1" rowspan="1">RANDOMCASE </td>
<td colspan="1" rowspan="1">REPEAT </td>
<td colspan="1" rowspan="1">UPPERCASE </td>
</tr><tr>
<td colspan="2" rowspan="1">#FLOPs matched to GPT-3 Large </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">Transformer </td>
<td colspan="1" rowspan="1">Token 31.1 </td>
<td colspan="1" rowspan="1">29.9 </td>
<td colspan="1" rowspan="1">27.1 </td>
<td colspan="1" rowspan="1">27.8 </td>
<td colspan="1" rowspan="1">38.9 </td>
<td colspan="1" rowspan="1">30.9 </td>
<td colspan="1" rowspan="1">20.2 </td>
</tr><tr>
<td colspan="1" rowspan="1">LlamaByte(W1024) </td>
<td colspan="1" rowspan="1">30.4 </td>
<td colspan="1" rowspan="1">28.1 </td>
<td colspan="1" rowspan="1">29.3 </td>
<td colspan="1" rowspan="1">27.2 </td>
<td colspan="1" rowspan="1">38.5 </td>
<td colspan="1" rowspan="1">30.7 </td>
<td colspan="1" rowspan="1">36.9 </td>
</tr><tr>
<td colspan="1" rowspan="1">LlamaByte(Global) </td>
<td colspan="1" rowspan="1">31.1 </td>
<td colspan="1" rowspan="1">28.1 </td>
<td colspan="1" rowspan="1">29.7 </td>
<td colspan="1" rowspan="1">27.3 </td>
<td colspan="1" rowspan="1">39.0 </td>
<td colspan="1" rowspan="1">31.0 </td>
<td colspan="1" rowspan="1">36.6 </td>
</tr><tr>
<td colspan="1" rowspan="1">MambaByte </td>
<td colspan="1" rowspan="1">29.8 </td>
<td colspan="1" rowspan="1">27.9 </td>
<td colspan="1" rowspan="1">29.9 </td>
<td colspan="1" rowspan="1">27.1 </td>
<td colspan="1" rowspan="1">39.6 </td>
<td colspan="1" rowspan="1">30.9 </td>
<td colspan="1" rowspan="1">34.5 </td>
</tr><tr>
<td colspan="1" rowspan="1">SpaceByte </td>
<td colspan="1" rowspan="1">30.7 </td>
<td colspan="1" rowspan="1">29.8 </td>
<td colspan="1" rowspan="1">33.5 </td>
<td colspan="1" rowspan="1">29.5 </td>
<td colspan="1" rowspan="1">47.8 </td>
<td colspan="1" rowspan="1">34.3 </td>
<td colspan="1" rowspan="1">38.1 </td>
</tr><tr>
<td colspan="1" rowspan="1">SpaceByte++</td>
<td colspan="1" rowspan="1">Byte 31.0 </td>
<td colspan="1" rowspan="1">30.9 </td>
<td colspan="1" rowspan="1">35.8 </td>
<td colspan="1" rowspan="1">29.3 </td>
<td colspan="1" rowspan="1">54.0 </td>
<td colspan="1" rowspan="1">36.2 </td>
<td colspan="1" rowspan="1">36.4 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(pool) </td>
<td colspan="1" rowspan="1">30.5 </td>
<td colspan="1" rowspan="1">31.2 </td>
<td colspan="1" rowspan="1">35.4 </td>
<td colspan="1" rowspan="1">29.6 </td>
<td colspan="1" rowspan="1">53.4 </td>
<td colspan="1" rowspan="1">36.1 </td>
<td colspan="1" rowspan="1">37.3 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(space) </td>
<td colspan="1" rowspan="1">30.8 </td>
<td colspan="1" rowspan="1">31.2 </td>
<td colspan="1" rowspan="1">38.6 </td>
<td colspan="1" rowspan="1">29.4 </td>
<td colspan="1" rowspan="1">54.0 </td>
<td colspan="1" rowspan="1">36.8 </td>
<td colspan="1" rowspan="1">38.2 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(1-stage) </td>
<td colspan="1" rowspan="1">31.2 </td>
<td colspan="1" rowspan="1">31.1 </td>
<td colspan="1" rowspan="1">35.4 </td>
<td colspan="1" rowspan="1">29.9 </td>
<td colspan="1" rowspan="1">54.1 </td>
<td colspan="1" rowspan="1">36.4 </td>
<td colspan="1" rowspan="1">37.2 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(2-stage) </td>
<td colspan="1" rowspan="1">30.8 </td>
<td colspan="1" rowspan="1">32.1 </td>
<td colspan="1" rowspan="1">39.3 </td>
<td colspan="1" rowspan="1">30.4 </td>
<td colspan="1" rowspan="1">57.1 </td>
<td colspan="1" rowspan="1">38.0 </td>
<td colspan="1" rowspan="1">39.0 </td>
</tr><tr>
<td colspan="4" rowspan="1">#FLOPs matched to GPT-3 XL </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">Transformer </td>
<td colspan="1" rowspan="1">Token 31.6 </td>
<td colspan="1" rowspan="1">30.7 </td>
<td colspan="1" rowspan="1">28.0 </td>
<td colspan="1" rowspan="1">28.5 </td>
<td colspan="1" rowspan="1">43.0 </td>
<td colspan="1" rowspan="1">32.3 </td>
<td colspan="1" rowspan="1">22.2 </td>
</tr><tr>
<td colspan="1" rowspan="1">SpaceByte++</td>
<td colspan="1" rowspan="1">30.9 </td>
<td colspan="1" rowspan="1">32.1 </td>
<td colspan="1" rowspan="1">40.3 </td>
<td colspan="1" rowspan="1">30.6 </td>
<td colspan="1" rowspan="1">58.5 </td>
<td colspan="1" rowspan="1">38.5 </td>
<td colspan="1" rowspan="1">38.5 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(space) </td>
<td colspan="1" rowspan="1">31.2<br>B </td>
<td colspan="1" rowspan="1">33.2 </td>
<td colspan="1" rowspan="1">41.9 </td>
<td colspan="1" rowspan="1">31.8 </td>
<td colspan="1" rowspan="1">60.7 </td>
<td colspan="1" rowspan="1">39.8 </td>
<td colspan="1" rowspan="1">40.5 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(1-stage) </td>
<td colspan="1" rowspan="1">yte<br>30.9 </td>
<td colspan="1" rowspan="1">32.7 </td>
<td colspan="1" rowspan="1">39.2 </td>
<td colspan="1" rowspan="1">31.2 </td>
<td colspan="1" rowspan="1">58.4 </td>
<td colspan="1" rowspan="1">38.6 </td>
<td colspan="1" rowspan="1">39.5 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(2-stage) </td>
<td colspan="1" rowspan="1">31.1 </td>
<td colspan="1" rowspan="1">34.7 </td>
<td colspan="1" rowspan="1">44.1 </td>
<td colspan="1" rowspan="1">33.0 </td>
<td colspan="1" rowspan="1">61.7 </td>
<td colspan="1" rowspan="1">40.9 </td>
<td colspan="1" rowspan="1">42.8 </td>
</tr></table>

**Robustness** **to** **Textual** **Perturbations.** Table 3 evaluates model robustness on HellaSwag with various textual pertur-bations, following protocols from BLT (Pagnoni et al. 2024). Importantly, these are the same checkpoints trained on clean FineWeb-Edu data used to evaluate Table 2), without any form of special data mix or augmentations that may improve character-level robustness. H-Net (2-stage) demonstrates substantially improved robustness compared to all baselines,with performance gaps exceeding those observed in standard benchmarks.

**Visualization** **of Tokenized** **Positions.** In Figure 4, we provide visualizations of the boundaries dynamically drawn by H-Net (1-stage) and H-Net (2-stage). The visualization offers several insights about how the model decides boundaries.

·Single-stage behavior: H-Net (1-stage) predominantly places boundaries at whitespace characters, closely mirroring the delimiters used by SpaceByte. This indicates that H-Net learns that word boundaries represent natural semantic units in text. This convergence to spacelike boundaries, discovered purely through end-to-end training,conversely validates SpaceByte's strong empirical performance.

·Hierarchical chunking patterns: The first stage of H-Net (2-stage) combines spacelike boundaries with first few characters of each word. This strategy helps the model because once the initial positions of a word are identified, the remaining characters become highly predictable.

·Content-aware chunking: One might question if H-Net's chunking decisions follow static rules, such as drawing boundaries only at certain fixed bytes (e.g., whitespace). However, as shown in the figure, H-Net often merges multiple words and spacelike characters based on content (examples include the backbone, such as, and (ii)).

·Perturbation behavior: Figure 16 shows the same example with textual perturbations such as removing whitespaces,which more prominently demonstrates that boundaries drawn by H-Net are based on content and context. In particular, it often still chunks in between semantic words even if the space is removed.

<!-- 14 -->

<!-- 77 69 74 68 20 6b 65 79 20 70 72 6f 70 65 72 74 69 65 73 20 74 68 61 wit h key P r 。 e r i e S t h a ☐□□ ☐□□□□□□☐ ☐ ☐ ☐ ☐ ☐ ☐ ☐ ☐□□☐ Stage 0 74 20 6d 61 6b 65 20 74 68 65 6d 20 73 75 69 74 61 62 6c 65 20 61 73 m a k th e m U b 1 e S ☐□□□ ☐□ - ☐ □ ☐ ☐ ☐ ☐☐ ← JL Stage 0 20 74 68 65 20 62 61 63 6b 62 6f 6e 65 20 6f 66 20 67 65 5e 65 72 61 t h b k b e g n ☐☐☐☐ ☐ ☐☐ ☐ ☐☐ ☐ ☐☐ ☐☐☐☐ ☐☐ ←-L JL Stage 0 6c 20 66 6f 75 6e 64 61 74 69 6f 6e 20 6d 6f 64 65 6c 73 20 6f 70 65 f 0 U n d t i m O d 1 p ☐☐☐☐☐ ☐ ☐ ☐ ☐ ☐ ☐ ☐ ☐ ☐ ☐ ☐ ☐☐ JL Stage 0 ← 72 61 74 69 6e 67 20 6f 6e 20 73 65 71 75 65 6e 63 65 73 20 20 28 69 r a t i n g O 5 0 u n C e ( ☐☐□□□☐ - ☐☐ ☐□□□□□□□□□ ☐☐ Stage 0 29 20 48 69 67 68 20 75 61 6c 69 74 79 3a 20 73 65 6c 65 63 74 69 ) H i g h q U a 1 i t y 5 e e C t i ☐☐☐□□☐ - ☐ □☐ ☐☐ ☐ -- LL ☐ ☐ ☐ ☐ ←-LL Stage 0 76 69 74 19 20 62 69 6 Ge 67 73 20 73 74 72 6F 6e 67 20 70 65 72 66 V ity br i n g 5 5 。 n g P e r ☐☐□☐ ☐☐□□□□☐ ☐□□□□□ ☐☐□□ Stage 0 ← 6F 72 72 6d 65 6 6e 20 6 65 20 6d 6E 64 6 6c 69 or m a n C 。 n d e n m d ☐☐□□□□□ ☐□☐ - ☐□ ☐ ☐ ☐ ☐ ☐☐ Stage 0 ← 2 9 65 73 20 73 75 63 68 20 73 20 6c 61 6e 67 75 61 67 65 20 61 ti e S 5 u C h a S 1 0 g u a g e a ☐□□ ☐ ☐□ ☐ ☐ ☐ ☐□ ☐ ☐ ☐ ☐ ☐ ☐ Stage 0 ← 6e 64 20 67 65 6e 6d 69 63 73 2e 20 28 69 69 29 20 46 61 73 74 n d g e n m 5 ( ii) F a 5 t ☐ □ ☐ Stage 0 -->

(a)H-Net (1-stage), using 6-DC.

Figure 4**: Visualization of boundaries drawn by H-Net.**The sample text is quoted verbatim from A. Gu and Dao (2023), and the colored boxes indicate positions where $b_{t}=$ 1. (a) H-Net (1-stage) draws boundaries at spacelike bytes,which is very similar to SpaceByte. (b) The boundaries ofthe first stage in H-Net (2-stage) are focused on spacelike bytes,and starting characters of each word. The second stage of H-Net (2-stage) chunks the text into meaningful units, such as words or numberings (i.e., '(ii)'). We can also observe that it often chunks multiple words that form one semantic group; for example, 'the backbone' and 'such as'.

77 69 74 68 206b 65 79 20 70 72 6f 70 6572 74 69 65 73 20 7468 61

<!-- W i t h ke y pro P e r t i e 5 tha ☐ ☐ ☐ ☐.☐□ -- ☐ - L J ☐ ☐□☐☐ ☐ ☐ ☐ ☐ Stage 0 ☐ □ .☐ ☐ Stage 1 74 20 6d 61 6b 65 20 74 68 65 6d 20 73 75 69 74 61 62 6c 65 20 61 73 t m a k e th e m 5 u it a b 1 e a ☐ ☐ ☐ ☐□□□ ☐ -- ☐☐ ☐ ☐ ☐□ JLL Stage 0 ☐ - ☐☐ Stage 1 20 74 68 65 20 62 61 63 6b 62 6f 6e 65 20 6f 66 20 67 65 6e 65 72 61 t h b C k b O n 。 f g e n a ☐ ☐ ☐ ☐ ☐ ☐ - ☐ - ☐☐ - . ☐ ☐ ←-L Stage 0 ☐ ☐ ☐ - ☐☐ ←-L JL Stage 1 6c 20 66 6f 75 6e 64 61 69 6f 6e 20 6d 6f 64 65 6c 73 20 6f 70 65 f 0 U d t i m 。 d e 1 5 p ☐☐ ☐ ☐ ☐ ☐□□ ☐ ← JLL JL Stage 0 ☐ ☐ ☐ ☐ Stage 1 72 61 74 69 6e 67 20 6 6e 20 73 65 71 75 65 6e 63 65 73 2e 20 28 69 a t i n g 。 n 5 e q u e n c 5 ( i ☐□□□□☐ ☐☐ ☐ . - ☐☐ ☐ ☐ ☐ ☐ ☐ JL JLL LL→ Stage 0 - JL ☐ ☐ ☐ -- ☐ ← JLJL Stage 1 29 20 48 69 67 68 20 71 75 61 6c 69 74 79 3a 20 73 65 60 65 63 69 - H ☐ g h q U a - i t y : 5 e 1 e C t i - - ☐□ ☐☐□□□□ -- ☐ - - ☐□ ☐ ☐ ←- J -L L Stage 0 ☐ ☐☐ 票☐ ☐☐ ☐ ☐ ←-1 JL Stage 1 76 69 74 79 20 62 72 69 6e 67 73 20 73 74 72 6f 6e 67 20 70 65 72 66 V i t y b r i n g 5 5 tr 。 n g P e r f ☐☐ ☐ -- ☐☐ ☐ ☐ -- - ☐ ☐□ L JLL JLL ☐□□ -LL Stage 0 - ☐□ - ☐ □ - ☐□ Stage 1 6f 72 6d 61 6e 63 65 20 6F 6e 20 64 65 6e 73 65 20 6d 6f 64 61 6c 69 0 r m n C 0 n d e e m 。 d a 1 i ☐☐ ☐☐ ☐ - - ☐ ☐ ☐ - -- JLIL ☐LL ☐☐ Stage 0 - - ☐ ☐ - ☐ □□ JL JL Stage 1 74 69 65 73 20 73 75 63 68 20 61 73 20 6c 61 60 67 75 61 67 65 20 61 t i e 5 U c h 1 a g g ☐□□ ☐ ☐☐ ☐☐ ☐ ☐□□☐ ☐ ☐ Stage 0 ☐ ☐ ☐ Stage 1 6e 64 20 67 65 60 6 6d 69 63 73 20 20 28 69 69 29 20 45 61 73 74 n d g n 0 m C 5 1 i i - F a 5 JLLUUUUUL Stage 0 ☐ ☐ ☐☐☐☐☐☐☐ Stage 1 -->

(b) H-Net (2-stage), using (3,3)-DC.

#### 3.2 Alternate Language Datasets

Besides conventional language modeling, we also examine three other language modeling settings: Chinese, code, and DNA. These three settings present distinct challenges for traditional language-modeling pipelines:

·Chinese characters consist of 3 utf-8 encoded bytes each and Chinese language does not have natural spaces; thus,constructing a vocabulary or pickking boundaries requires special consideration.

·Code contains much more whitespace than typical language, which allows greater compressibility if handled properly.It also has latent hierarchical structure that can be leveraged for improved reasoning capabilities.

·DNA, while having a small vocabulary, does not have any natural tokenization cues and instead must be processed

<!-- 15 -->

<!-- Chinese (F.-Edu-Chinese-V2.1) Code (Pile Github) 0.90- 0.44- 0.88 0.42- 0.86- auhc-ad-saS UoIepIeA 0.84- 0.40- 0.82- 0.80- 0.38- 0.78- 0.36- 0.76- 0.74- $20B$ 40B 50B 60B 80B 90B 100B 20B 30B 40B 50B 80B Total Training Bytes Total Training Bytes H-Net (2-stage) H-Net(space) (BPE) Transformer -->

Figure 5**: Validation Bits-per-byte (BPB) throughout training on Chinese language and code modeling.** H-Net (space) and H-Net (2-stage) are byte-level, while the Transformers use the Llama-3 tokenizer which was designed for multilingual. H-Net clearly outperforms both Transformer and H-Net (space) on Chinese language modeling,which does not have space-like segmentation cues, with lower BPB than H-Net (space) throughout training and crossing over with Transformer after around 25B bytes. On code, both H-Net (2-stage) and SpaceByte++ significantly outperform BPE Transformer. Final post-decay results can be found in Table 4.

as raw base pairs.

H-Net can operate on raw data without the need for handcrafted features (whether vocabulary or deliniation cues); it therefore provides a natural architecture that can operate naturally on any language.

**Experimental** **setup** **for** **Chinese** **and** **code.** On Chinese and code, we train three models at the 1.3B GPT-3 XL scale: H-Net (2-stage), H-Net (space), and Transformer. We maintain the same bytes per gradient step (256 batch size with 8192 utf-8 encoded bytes per example) as the main text experiments. For the H-Net (2-stage), we use the same target downsampling ratio $\left(N^{0}=N^{1}=3\right)$ as the main experiments. Unlike BPE or spacelike-based tokenization, whose downsampling ratios can vary widely by dataset, H-Net allows for using similar compute budgets without much adjustment.For H-Net (space), we use the same definition of spacelike as the original SpaceByte paper (Slagle 2024), and for BPE,we use the Llama3 tokenizer, as the GPT2 tokenizer attains very poor downsampling ratios on both datasets. Despite this change, both H-Net (space) and Transformer (BPE) still have highly varied downsampling ratios between ordinary (primarily English) language, Chinese, and code. On the other hand, H-Net can adhere to a target ratio regardless of dataset,chunking into concepts at appropriate ratios.

Even with the Llama3 tokenizer, we find that H-Net (2-stage) scales better than BPE Transformer and H-Net (space) on both Chinese and code (Figure 5), and achieves lower compression after the decay phase (Table 4). We additionally measure the performance of each Chinese-language model on the Chinese split of XWinograd, a multilingual Winograd Schema Challenge (Muennighoff, T.Wang, et al. 2023), where H-Net (2-stage) is significantly better than H-Net (space) which is significantly better than Transformer (Table 4).

**DNA** **(Human** **Genome).** DNA is a setting that presents both a unique promise and challenge for hierarchical modeling.For one, handcrafted tokens do not work well on DNA, due to the lack of segmentation cues. Additionally,the same sequence of base pairs may serve different functions (e.g., depending on whether or not the pair is inside a gene or not).Consequently, a naive BPE-based approach may not work either. On the other hand, DNA can exhibit higher resolution structure (e.g., codons, various regulatory elements), suggesting that there is room for principled hierarchical modeling.Indeed, state-of-the-art DNA models (Brixi et al. 2025) operate directly on base pairs (A, C, G,T) with implicit hierarchical structure.

Thus, we evaluated four models on DNA: two isotropic models (pure Transformer and pure Mamba-2) operating at the base-pair level, and two corresponding H-Net (1-stage) with Transformer and Mamba-2 as the main network.Each model

<!-- 16 -->

Table 4: **Architecture details and model benchmarks for Chinese and code models.** BPIC (defined in Table 2) denotes the compression between the main network and outermost stage (bytes). Each H-Net used (3,3)-DC, targeting an inner downsampling ratio of 9. However, the resulting BPIC was significantly different, indicating that code is much easier to compress than Chinese. In terms of results, H-Net (2-stage) performs better than both H-Net (space) and BPE Transformer on Chinese, which is reflected in the downstreams. On the other hand, H-Net (2-stage) achieves similar performance to H-Net (space) on code, and both H-Net models perform significantly better than Transformer.

<table border="1" ><tr>
<td colspan="1" rowspan="2">MODEL </td>
<td colspan="4" rowspan="1">CHINESE </td>
<td colspan="3" rowspan="1">CODE </td>
</tr><tr>
<td colspan="1" rowspan="1">BPIC </td>
<td colspan="1" rowspan="1">MAIN ARCH. </td>
<td colspan="1" rowspan="1">VAL. BPB↓</td>
<td colspan="1" rowspan="1">XW-ZH.ACC.↑</td>
<td colspan="1" rowspan="1">BPIC </td>
<td colspan="1" rowspan="1">MAIN ARCH. </td>
<td colspan="1" rowspan="1">VAL.BPB↓</td>
</tr><tr>
<td colspan="1" rowspan="1">Transformer </td>
<td colspan="1" rowspan="1">3.62 </td>
<td colspan="1" rowspan="1">T15 </td>
<td colspan="1" rowspan="1">0.7404 </td>
<td colspan="1" rowspan="1">0.599 </td>
<td colspan="1" rowspan="1">3.58 </td>
<td colspan="1" rowspan="1">T13 </td>
<td colspan="1" rowspan="1">0.3376 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net (space) </td>
<td colspan="1" rowspan="1">3.38 </td>
<td colspan="1" rowspan="1">T19 </td>
<td colspan="1" rowspan="1">0.7478 </td>
<td colspan="1" rowspan="1">0.629 </td>
<td colspan="1" rowspan="1">7.97 </td>
<td colspan="1" rowspan="1">T40 </td>
<td colspan="1" rowspan="1">0.3163 </td>
</tr><tr>
<td colspan="1" rowspan="1">H-Net(2-stage) </td>
<td colspan="1" rowspan="1">5.81 </td>
<td colspan="1" rowspan="1">T30 </td>
<td colspan="1" rowspan="1">0.7032 </td>
<td colspan="1" rowspan="1">0.663 </td>
<td colspan="1" rowspan="1">7.23 </td>
<td colspan="1" rowspan="1">T28 </td>
<td colspan="1" rowspan="1">0.3161 </td>
</tr></table>

<table border="1" ><tr>
<td colspan="1" rowspan="1">MODEL/ARCHITECTURE</td>
<td colspan="1" rowspan="1">PARAMS. </td>
<td colspan="1" rowspan="1">FINAL PPL.↓</td>
</tr><tr>
<td colspan="1" rowspan="1">Transformer (T9) </td>
<td colspan="1" rowspan="1">29M </td>
<td colspan="1" rowspan="1">2.769 </td>
</tr><tr>
<td colspan="1" rowspan="1">Mamba-2 (M10 </td>
<td colspan="1" rowspan="1">33M </td>
<td colspan="1" rowspan="1">2.757 </td>
</tr><tr>
<td colspan="1" rowspan="1">$\text {H-Net}(\text {M3T1+T15+M4})$</td>
<td colspan="1" rowspan="1">64M </td>
<td colspan="1" rowspan="1">2.705 </td>
</tr><tr>
<td colspan="1" rowspan="1">$\text {H-Net}(\text {M3T1+M15+M4})$</td>
<td colspan="1" rowspan="1">66M </td>
<td colspan="1" rowspan="1">2.697 </td>
</tr></table>

Table 5**: Model details and final performance on** **HG38.** We trained two isotropic models and two H-Net models, varying the main network architecture (Trans-former or Mamba-2). Each H-Net model outperforms the corresponding isotropic model. We empirically find that the $\mathcal {E}^{0}=\mathrm {M}3\mathrm {\sim T}1$ encoder architecture slightly outper-forms a pure Mamba-2 encoder $\mathcal {E}^{0}=\mathrm {M}4$ (Appendix E.3).

<!-- Perplexity on HG38 Transformer 2.95- Mamba-2 2.90- H-Net(Trans.) H-Net (Mamba) 2.85- 2.80- 2.75- 3.6x more data efficient than isotropic model 1B 28 8B Training Base Pairs Seen -->

Figure 6**: Scaling performance on HG38** during the stable phase of training. Each H-Net model achieves the same pre-decay perplexity of the corresponding isotropic model with approximately 3..6x less data.

was trained with a learning rate of 5· $10^{-3}$ for modules at the base-pair resolution. For the H-Net models, we used a downsampling ratio of $N^{0}=3.$ All models were trained with a $d_{\text {model}}$  of 512, which was used for all isotropic modules of H-Net (including the main network).

Previous work has shown that SSMs show improved DNA modeling ability compared to Transformers (A. Gu and Dao 2023),and we find that this effect is preserved when examining Transformers vs. Mamba-2 as the main network (see Table 5).This finding suggests that existing layer selection principles can be applied when deciding main network architecture.In fact, by directly comparing the perplexity curves during the stable phase of training (Figure 6), we find that H-Net models can achieve similar performance to isotropic models with just 3.6x the amount of data, a finding that holds for both choices of main network architecture.

#### 3.3 Ablation Studies

For ablation studies, we employ H-Net at Large scale following the configurations in Table 1, training on 36B tokens randomly sampled from FineWeb-Edu.

**Importance** **of** **Components** **in** **H-Net.** Figure 7 illustrates the impact of each architectural component on both model performance and compression ratio $\left(L^{+1}/L^{}\right)$ stability during training. We conduct three targeted ablations: (i) using direct upsampling $\tilde {z}_{t}=\hat {z}_{t}$ by removing the smoothing module (w/o smoothing), (ii) replacing the routing module that is based on scaled cosine similarity, with direct probability prediction from individual inputs (w/o cosine routing), and (iii) skipping the straight-throughestimator in equation (9) (w/o STE).

<!-- 17 -->

<!-- Bits-per-byte (2-stage) Compression Ratio $L^{1}/L^{0}$ Compression Ratio $L^{2}/L^{1}$ 0.38- 1.05- 0.44- 1.00- 0.37- 0.42- 0.95- 0.36- 0.40- 0.90- 0.35- 0.38- 0.85- 0.34- 0.36- 0.80- 125B 25B $50B$ $75B$ 125B Total Training Bytes Total Training Bytes Total Training Bytes H-Net w/o smoothing w/o cosine routing w/o STE -->

Figure 7: **Ablation** **study** **on** **key** **H-Net** **components** showing validation BPB (left) and compression ratios for the first stage $L^{1}/L^{0}$ (center) and second stage $L^{2}/L^{1}$ (right) during training. Using H-Net (2-stage), we evaluate the impact of removing three components: the smoothing module (w/o smoothing), the similarity-based routing module (w/o cosine routing), and Straight-Through Estimator (w/o STE).

<!-- Bits-per-byte Compression Ratio $L^{1}/L^{0}$ 0.94- 0.92- 0.24- 0.90 0.23- 0.88- 0.86- 0.22- 0.84- 0.82- 0.21 0.80- 40B 60B 100B 120B 140B 40B 60B 80B 100B Total Training Bytes Total Training Bytes M4-M4 M2T1-T1M2 T1M2-M2T1 T2-T2 -->

Figure 8**: Encoder-decoder architecture ablation using raw byte inputs.** Validation BPB (left) and compression ratio $L^{1}/L^{0}$  (right) for H-Net (1-stage) throughout training. We evaluate four encoder-decoder $\left(\mathcal {E}^{0}-\mathcal {D}^{0}\right)$ configurations: M4-M4,M2T1-T1M2 and T1M2-M2T1, and T2-T2, where M denotes Mamba layers and T denotes Transformer layers.

The smoothing module proves essential for stable training dynamics. Without this module, compression ratios fluctuate severely throughout training, preventing the model from learning consistent chunking boundaries. This instability directly manifests as substantial performance degradation, confirming that smooth gradient flow through the decompression process is crucial for effective end-to-end learning. While less critical than the smoothing module, both the similarity-based routing module and STE operation exhibit importance in training stability and final performance. These components help maintain consistent compression ratios and lead to more interpretable chunking patterns. The similarity-based approach particularly enables the model to identify natural linguistic boundaries (e.g., whitespaces, subwords) by comparing adjacent representations rather than making isolated predictions.

**Encoder** **&** **Decoder** **Layer** **Selection.** The composition of sequence mixing layers in H-Net's encoders and decoders substantially influences both compression efficiency and modeling capability. We systematically evaluate different ar-chitectural combinations using H-Net (1-stage) while fixing all other configurations in Table 1 the same. Four distinct

<!-- 18 -->

<!-- Bits-per-byte 0.94- T2-T2 T1M2-M2T1 0.92- M2T1-T1M2 0.90 M4-M4 0.88- 0.86- 0.84- 0.82- 40B $40B$ 60B 80B $80B$ 00B 120B 140B Total Training Bytes -->

Figure 9**: SpaceByte** $++$ **encoder-decoder architecture ab-lation using raw byte inputs.** We evaluate four encoder-decoder $\left(\mathcal {E}^{0}-\mathcal {D}^{0}\right)$ configurations: M4-M4, M2T1-T1M2 and T1M2-M2T1,and T2-T2, where M denotes Mamba layers and T denotes Transformer layers.

<!-- Bits-per-byte 1.000- T3-T3 T2M2-M2T2 0.975- M2T2-T2M2 0.950- T1M4-M4T1 M4T1-T1M4 0.925- M6-M6 0.900- 0.875 0.850- 0.825- $AOB$ 40 60B 80B 120B 140B Total Training Bytes -->

Figure 10**: Encoder-decoder architecture ablation us-ing BPE-tokenized inputs.** Assuming that GPT-2 tok-enizer serves as the outermost encoder-decoder(i.e., $\left.\mathcal {E}^{0}-\mathcal {D}^{0}\right)$  we evaluate six $\mathcal {E}^{1}-\mathcal {D}^{1}$  combinations: M6-M6, M4T1-T1M4,T1M4-M4T1,M2T2-T2M2, T2M2-M2T2, and T3-T3.

encoder-decoder $\left(\mathcal {E}^{0}-\mathcal {D}^{0}\right)$ pairings are tested: M4-M4, M2T1-T1M2, T1M2-M2T1, and T2-T2, where M denotes a Mamba-2layer and T denotes a Transformer layer. These combinations are chosen by keeping the symmetry and replacing each Transformer layer with two Mamba-2 layers, as they contain equivalent parameter counts $-12D^{2}$  for Transformer ( $4D^{2}$ for the attention mechanism and $8D^{2}$ for an MLP) vs. $\approx 6D^{}$ per Mamba-2 layer (no MLP).

Figure 8 and Figure 9 demonstrate that Mamba layers are essential for effective byte-level sequence processing. For both H-Net and $SpaceByte++$ ,the **pure Transformer configuration (T2-T2) exhibits by far the worst performance** **despite** **using** **more** **FLOPs** (it also down-compresses sequences poorly compared to other configurations, thus using more compute in the main network). This configuration struggles to compress byte sequences effectively, resulting in both computational waste and degraded modeling performance. Performance improves monotonically with increased Mamba layer allocation, achieving optimal results with the highest compression efficiency in the pure Mamba configuration (M4-M4). These findings align with recent research demonstrating SSMs' advantages over Transformers for fine-grained sequence modeling (Goel et al. 2022; Schiff et al. 2024), as corroborated by MambaByte's superior performance over LlamaByte in Figure 3.

A natural question arises: does the importance of Mamba layers (i) stem specifically from **processing** **fine-grained** **byte** **inputs,** or (ii) because **they are better for compressing information into the next stage, even at coarser input** **resolutions?** To investigate this hypothesis, we train a 1-stage H-Net on top of BPE-tokenized inputs processed by the GPT-2 tokenizer. We then evaluate six different encoder-decoder combinations.

·If hypothesis (i) holds, then we would expect different combinations of Mamba/Transformer layers in the encoder/de-coder to have similar performance, since it is known that they have similar performance on standard tokenized language modeling.

·If hypothesis (ii) holds, then we would expect that encoders/decoders using some Mamba layers to be better than pure Transformer layers.

As demonstrated in Figure 10, Mamba layers prove significantly important even when processing BPE tokens rather than raw bytes, providing evidence for the second hypothesis.

We hypothesis that, this consistent advantage across input granularities stems from fundamental architectural differences between SSMs and Transformers. While Transformers naturally store complete key-value caches for all positions, SSMs are designed to compress information into fixed-size states. This compression-oriented architecture aligns naturally with our chunking mechanism, which requires aggregating multiple input vectors into consolidated representations. The inherent compression capability of Mamba layers makes them particularly well-suited for the encoder and decoder roles in our hierarchical architecture (A. Gu 2025). Based on these findings, we employ Mamba layers throughout all encoders and

<!-- 19 -->

<!-- Bits-per-byte 0.86- H-Net (2-stage,hybrid) 0.84- H-Net (2-stage,T27) 0.82- 0.80- 0.78- 0.76- 0.74- 100B $150B$ $200B$ 300B Total Training Bytes -->

Figure 11**: Hybrid main network.** Bits-per-byte during the stable phase of training, for H-Net (2-stage) with Trans-former main stage and with hybrid main stage. The hybrid main stage scales better, similar to findings for standard token-based language models. This finding suggests that design principles for isotropic (tokenized) models can carry over to choices of the main network.

<!-- Bits-per-byte 1.05- LlamaByte (200M) LlamaByte MoE (860M) 1.00- H-Net (1-stage,680M) H-Net (2-stage,870M) 0.95- 0.90- 0.85- 0.80- $40B$ $60B$ $80B$ 140B Total Training Bytes -->

Figure 12: **Comparison to Mixtures-of-Experts.** Bits-per-byte comparison of H-Net (both 1-stage and 2-stage) to LlamaByte-MoE,which is a FLOPs-matched MoE model that uses a similar number of parameters as H-Net (2-stage). Both  H-Nets perform much better than LlamaByte-MoE, implying that H-Net's capabilities do not just come from sparsity.

decoders in our final H-NNet configuration, as detailed in Table 1.

These findings transfer to more general hierarchical structures (such as a 2-stage H-Net at the byte level), in which case the outermost encoder and decoder layers $\left(\mathcal {E}^{0}\right.$ and $\left.\mathcal {D}^{0}\right)$ serve a similar role as the GPT-2 tokenizer and the inner layers $\left(\mathcal {E}^{1}\right.$ nd $\left.\mathcal {D}^{1}\right)$ would share similar findings of benefiting from using Mamba layers.

**Hybrid** **Architectures** **for** **the** **Main** **Network.** We also aimed to understand the role of architecture selection in the main network. To this end, we compared H-Net (2-stage) with an identical model where we replaced the Transformer stack with a hybrid model containing both 20 Mamba-2 and 7 Transformer layers interleaved in a 3:1 ratio. Hybrid architectures have shown promise in isotropic (BPE) models (Waleffe et al. 2024), and similarly perform better for our choice of main network (Figure 11).

<!-- 20 -->

Table 6: **Related** **architectures.** Comparison of related architectures, particularly those focused on byte-level modeling.H-Net is the first architecture that enables dynamic, mulIti-stage hierarchies. Extended discussion is provided in Appendix A.

<table border="1" ><tr>
<td colspan="1" rowspan="1">CLASS </td>
<td colspan="1" rowspan="1">AUTOREGRESSIVE </td>
<td colspan="1" rowspan="1">CHUNKING MECHANISM </td>
<td colspan="1" rowspan="1">MULTI-STAGE HIERARCHY </td>
<td colspan="1" rowspan="1">EXAMPLE ARCHITECTURES </td>
</tr><tr>
<td colspan="1" rowspan="2">Isotropic </td>
<td colspan="1" rowspan="1">x </td>
<td colspan="1" rowspan="1">-</td>
<td colspan="1" rowspan="1">-</td>
<td colspan="1" rowspan="1">ByT5 </td>
</tr><tr>
<td colspan="1" rowspan="1">✓</td>
<td colspan="1" rowspan="1">-</td>
<td colspan="1" rowspan="1">-</td>
<td colspan="1" rowspan="1">MambaByte </td>
</tr><tr>
<td colspan="1" rowspan="2">Hierarchical (static) </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">k-width pooling </td>
<td colspan="1" rowspan="1">✓</td>
<td colspan="1" rowspan="1">Funnel-Transformer<br>Canine<br>Charformer </td>
</tr><tr>
<td colspan="1" rowspan="1">✓</td>
<td colspan="1" rowspan="1">k-width pooling </td>
<td colspan="1" rowspan="1">✓</td>
<td colspan="1" rowspan="1">Hourglass Transformer SaShiMi<br>MegaByte<br>Block Transformer<br>MBLM<br>AU-Net 3 </td>
</tr><tr>
<td colspan="1" rowspan="3">Hierarchical (external) </td>
<td colspan="1" rowspan="1">x </td>
<td colspan="1" rowspan="1">delimiters </td>
<td colspan="1" rowspan="1">x </td>
<td colspan="1" rowspan="1">eByte<br>WSF </td>
</tr><tr>
<td colspan="1" rowspan="2">✓</td>
<td colspan="1" rowspan="1">delimiters </td>
<td colspan="1" rowspan="1">x </td>
<td colspan="1" rowspan="1">DPT (Whitespaces)<br>SpaceByte<br>AU-Net 2 </td>
</tr><tr>
<td colspan="1" rowspan="1">entropy </td>
<td colspan="1" rowspan="1">x </td>
<td colspan="1" rowspan="1">DPT (Entropy)<br>BLT </td>
</tr><tr>
<td colspan="1" rowspan="3">Hierarchical (dynamic) </td>
<td colspan="1" rowspan="3">✓</td>
<td colspan="1" rowspan="1">soft matching </td>
<td colspan="1" rowspan="1">x </td>
<td colspan="1" rowspan="1">MANTa </td>
</tr><tr>
<td colspan="1" rowspan="1">soft gating </td>
<td colspan="1" rowspan="1">x </td>
<td colspan="1" rowspan="1">MrT5 </td>
</tr><tr>
<td colspan="1" rowspan="1">stochastic reparameterization </td>
<td colspan="1" rowspan="1">x </td>
<td colspan="1" rowspan="1">DPT(Gumbel) </td>
</tr><tr>
<td colspan="1" rowspan="1">Hierarchical (dynamic) </td>
<td colspan="1" rowspan="1">✓</td>
<td colspan="1" rowspan="1">dynamic chunking </td>
<td colspan="1" rowspan="1">✓</td>
<td colspan="1" rowspan="1">H-Net </td>
</tr></table>

<!-- 21 -->

**Comparison** **to** **Mixtures-of-Experts.** H-Net can be viewed as a form of dynamic sparsity similar to Mixtures of Experts (MoEs), in that they are able to improve performance by using more parameters, all while keeping the total FLOPs budget constant. We were interested in understanding whether or not its performance benefits were simply due to increasing sparsity. We compare against a sparsified version of LlamaByte (byte-level isotropic Transformer model) at the Large scale with a standard Mixture-of-Experts recipe (Fedus,Zoph, and Shazeer 2022) and similar parameter count as ours(Figure 12).While sparsity does improve LlamaByte performance, it is still far worse than either FLOPs-matched H-Net (1-stage)or H-Net (2-stage), even with similar parameter count. We interpret this result as: H-Net not only achieves sparsity, but does so in a semantically meaningful manner, which allows for better scaling than even generic sparse methods.

### 4 Discussion

**Related** **Work.** Table 6 summarizes related models, particularly those motivated by byte-level language modeling. These methods are described in detail in Appendix A, which provides an extended related work.

**Distillation.** For new architectures, showing that they can be distilled from standard pretrained Transformers can result in stronger new models with substantially reduced training (Bick et al. 2024). In Appendix F, we investigate this for H-Net by initializing the main network from a pretrained Llama checkpoint and learning the encoder and decoder networks.With less than 200B bytes of training, the resulting model strong performance much better than if it were trained from scratch, although still worse than the teacher model. Our distillation procedure is perhaps currently the most efficient way of creating an end-to-end byte-level model, but expect that it can be further improved.

**Efficiency.** Because of the dynamic nature of our model, it requires different considerations in making both the training pass and inference step efficient. Our implementation incorporated several engineering techniques already, such as handling variable sequence lengths within a mini-batch using specialized kernels provided by Dao (2024) and Dao and A. Gu (2024).Because of the different architectural considerations, it is difficult to compare to more standard pipelines; our current implementation may be approximately up to2x slower than an isotropic model during training.

Note that the memory usage of our model is also dynamic, unlike standard sequence models, so other edge cases may happen, such as unlucky batches of sequences that are too long and overflow the device memory. Relatedly, one difficulty with stepping H-Net in batched mode is that different tokens in the batch may require different amounts of compute.

We believe that such considerations are not fundamental and will be an important subject of future work; just as how related dynamic sparsity and conditional compute methods such as mixture-of-experts and speculative decoding (Leviathan,Kalman, and Matias 2023) benefited from years of dedicated engineering improvements.

**Deeper** **Hierarchies.** H-Net is the first dynamic hierarchical model that can recursively nest its chunking strategy (see Table 6 and Appendix A). In this paper, we showed that iterating H-Net from 0 stages (i.e. an isotropic model) to 1 stage and from 1 stage to 2 stages consistently improves performance. We did not attempt a 3-stage H-Net at all for simplicity.Testing if H-Net can be iterated even deeper remains an immediate direction to explore.

**Global** **Sequence** **Model** **Considerations.** Much research on sequence model architectures has focused on individual layers, where the tradeoffs are often quite direct. For example, recurrent models such as state space models (A. Gu 2023;A. Gu and Dao 2023) and linear attention variants (Katharopoulos et al. 2020; S. Yang, Kautz, and Hatamizadeh 2024;S.Yang,B. Wang, Shen, et al. 2023; S. Yang, B. Wang,Y.Zhang, et al. 2024) compress arbitrarily long sequences into fixed-size hidden states, offering higher efficiency at the expense of precise retrieval of information (e.g. struggling with recall (Jelassi et al. 2024) or retrieval (Waleffe et al. 2024)).

H-Net, however, is a global architectural design that is simultaneously orthogonal to, but may have interactive effects with,the choice of individual layers. For example, using deeper hierarchies with exclusively recurrent layers would preserve linear computation (in sequence length) but logarithmic state size, resembling newer sequence model layers such as log-linear attention (Guo et al. 2025) and Prefix Scannable Models (Yau et al. 2025), but with dynamic hierarchies.Similarly, the recursive compression of sequence length may alleviate their limitations in retrieval on long sequences.This may be considered a form of dynamic state allocation. This paper has not focuused on such implications, which would be a possible direction for future research.

<!-- 22 -->

**Latennt** **Test-Time** **Compute.** Test-time compute techniques, exemplified by Chain-of-Thought (Wei et al. 2022),have been shown to improve model performance on a variety of reasoning benchmarks (Muennighoff, Z. Yang, et al. 2025;OpenAI 2024). Recent work has explored including latent representations (as opposed to just tokens) in the reasoning process (Geiping et al. 2025; Hao et al. 2024), culminating in "Recurrent Depth" models that roll out an RNN for as many steps as needed before emitting a token (Geiping et al. 2025). As discussed in 2.4, H-Net is also capable of dynamically choosing how much compute to use per byte generated; thus, it can be viewed as a model that can dynamically allocate latent test-time compute as well. We did not explore the implications of this interpretation of H-Net, which also remains a potential direction for future work.

**Long** **Context.** Similarly, an effect of the global hierarchical structure may be improved long context abilities,which is a common motivation for hierarchical models (Chang et al. 2017; Koutnik et al. 2014). Much research on sequence models again focuses on long context at the layer level (A. Gu and Dao 2023; Poli et al. 2023; Vaswani et al. 2017), and we hypothesize that H-Nets may provide general long context improvements in an orthogonal direction.

**Sparsity.** H-Net can be viewed as a form of dynamic sparsity or conditional computation, and is related to concepts such as mixture-of-experts (MoE) (Fedus, Zoph, and Shazeer 2022; Shazeer et al. 2017) and mixture-of-depths (Raposo et al.2024). We showed that at the byte level, DC is much more effective than MoE when controlled for parameters and compute (Figure 12), and leave fleshing out further connections and comparisons for future work. We also note that H-Net can be viewed as orthogonal to MoE, which can be applied to sparsify any MLP layers within an H-Net.

**Scale.** The largest models in this paper were FLOP-matched to the equivalent of a 1.3B parameter Transformer. While we believe that this provides sufficient evidence for the effectiveness of this approach, it remains to validate H-Net at larger model sizes of 3B, 7B, and beyond. We note that while we observed no instabilities at our model sizes,the added complexity of H-Net and inherent challenges of learning end-to-end discrete selection problems may require more serious investigation of potential stability challenges at larger scale.

**Scaling** **Laws.** Formally estimating the scaling behavior of a model requires calculating scaling law coefficients that sweep across a large range of model sizes and compute horizons (Hoffmann et al. 2022; J. Kaplan,McCandlish,Henighan,Tom B.Brown, et al. 2020b). We did not pursue this formal approach in this paper due to resource constraints.

Instead,we used a simpler heuristic for the scaling behavior of our models, at least with respect to data. We note that

·essentially all modern models live in the “overtrained" regime (with respect to the formal scaling laws) due to inference considerations at deployment (Touvron, MMartin, et al. 2023); and

·these overtrained models often use modern schedulers that have extended periods of constant learning rates (DeepSeek-AI 2024; S. Hu et al. 2024).

Thus, we decided to use the models' losses during the constant phase as a proxy for how quickly they improve with data.We believe this still provides useful insight into scaling behaviors, and a more dedicated analysis of formal scaling laws remains an important topic for future work.

**BPB** **Calculation.** For baseline BPE tokenized models throughout this work, we used the standard bits-per-byte (BPB)calculation of simply rescaling the negative log-likelihood (or log perplexity) by the average number of bytes per token (L. Gao, Biderman, et al. 2020; Slagle 2024; J. Wang et al. 2024). However, this is not strictly speaking a correct BPB estimate for tokenized models, as it assumes that the probability the model outputs a string is equal to the probability of the model outputting the greedy tokenization of the string.

Depending on how the model is trained, it is possible the model can output other tokenization sequences with nonzero probability. There are an exponential number of these, so computing the exact BPB is intractable; however, concurrent work (Vieira et al. 2024) shows that the standard BPB calculation indeed overestimates BPB. Due to the high computational overhead of estimating the true BPB, we only provide the standard (inexact) value; nevertheless, H-Net's superior performance on downstreams provides supporting evidence that it scales better than BPE models.

<!-- 23 -->

## 5 Conclusion

Major advances in deep learning have resulted from powerful architectural innovations enabling previously-handcrafted features to belearned from data, from CNNs learning visual features (Krizhevsky, Sutskever, and Hinton 2012) to Trans-formers discovering linguistic patterns (Vaswani et al. 2017). **H-Nets** similarly unlock the ability to remove another layer of pre-processing, such as tokenizers, and instead learn them end-to-end. This ability results from a set of new techniques we introduce that work together to form a **dynamic chunking** mechanism, which is able to learn content-and context-dependent discrete segmentation strategies through standard gradient-based optimization. A single-stage byte-level H-Net already exceeds the performance of standard tokenized language models, and recursive H-Nets with multiple stages of dynamic chunking further improve its scaling. H-Nets substantially remedy issues with tokenizers, display very strong performance on diverse languages and language-like modalities, and more broadly may serve as the backbone of general foundation models that can learn more with less processing.

<!-- 24 -->

