{"model": {"hierarchical_stages": 2, "dynamic_chunking_enabled": true, "cnn_config": {"input_channels": 22, "conv_layers": [{"out_channels": 16, "kernel_size": 3, "stride": 1, "padding": 1}, {"out_channels": 32, "kernel_size": 3, "stride": 1, "padding": 1}, {"out_channels": 64, "kernel_size": 3, "stride": 2, "padding": 1}], "activation": "LeakyReLU", "dropout_rate": 0.2, "batch_norm": false, "output_features": 8}, "lstm_config": {"hidden_size": 128, "num_layers": 1, "dropout": 0.2, "bidirectional": false, "attention_enabled": true, "attention_dim": 128, "output_features": 8}, "transformer_config": {"d_model": 256, "nhead": 4, "num_encoder_layers": 3, "num_decoder_layers": 6, "dim_feedforward": 2048, "dropout": 0.1, "activation": "gelu", "layer_norm_eps": 1e-05, "batch_first": true, "norm_first": true}, "dynamic_chunking_config": {"target_compression_ratios": [3, 3], "similarity_threshold": 0.5, "smoothing_enabled": true, "ratio_loss_weight": 0.03, "boundary_confidence_threshold": 0.8}, "dqn_config": {"state_dim": 17, "action_dim": 3, "hidden_layers": [512, 256, 128, 64], "activation": "LeakyReLU", "dropout_rate": 0.3, "dueling_enabled": true, "double_dqn_enabled": true, "noisy_nets_enabled": false, "prioritized_replay_enabled": true}}, "factor": {"selected_factors": ["close", "vol", "pct_chg", "momentum_10d", "ma_ratio_10d", "macd", "bb_ratio", "atr"], "_base_price_factors": ["open", "high", "low", "close", "pct_chg", "vol", "amount"], "factor_selection": {"auto_selection_enabled": false, "selection_method": "user_defined", "correlation_threshold": 0.8, "stability_window": 252}, "preprocessing": {"normalization_method": "robust_scaler", "outlier_treatment": "winsorize", "winsorize_limits": [0.01, 0.99], "missing_value_method": "forward_fill", "feature_engineering_enabled": true}}, "trading": {"initial_capital": 500000.0, "max_position_size": 0.8, "min_position_size": 0.05, "transaction_cost": 0.0015, "slippage": 0.0008, "execution_price": "close", "execution_timing": "close", "allow_short_selling": false, "risk_management": {"max_drawdown_threshold": 0.12, "stop_loss_threshold": 0.05, "take_profit_threshold": 0.1, "var_confidence_level": 0.05, "cvar_enabled": true, "risk_budget_allocation": true, "volatility_target": 0.18}, "position_sizing": {"method": "equal_weight", "kelly_fraction": 0.25, "rebalance_frequency": "daily", "min_trade_size": 0.01, "max_concentration": 0.2}}, "training": {"train_start_date": "2021-01-01", "train_end_date": "2023-12-31", "val_start_date": "2024-01-01", "val_end_date": "2024-12-31", "test_start_date": "2025-01-01", "test_end_date": "2025-06-01", "rl_config": {"episodes": 1000, "max_steps_per_episode": 252, "batch_size": 32, "learning_rate": 5e-05, "gamma": 0.99, "epsilon_start": 1.0, "epsilon_end": 0.01, "epsilon_decay": 0.995, "target_update_frequency": 100, "memory_size": 100000, "min_memory_size": 10000, "gradient_clip_norm": 1.0}, "optimizer_config": {"type": "AdamW", "lr": 0.0001, "weight_decay": 1e-05, "betas": [0.9, 0.999], "eps": 1e-08, "scheduler_type": "cosine_annealing", "scheduler_params": {"T_max": 1000, "eta_min": 1e-06}}, "lr_modulation": {"enabled": true, "outer_stage_multiplier": 2.0, "inner_stage_multiplier": 1.0, "adaptive_scaling": true}}, "environment": {"market_hours": ["09:30", "15:00"], "trading_calendar": "NYSE", "benchmark": "SPY", "hierarchical_env": {"time_scales": ["daily", "weekly", "monthly"], "decision_frequencies": [1, 5, 20], "reward_aggregation": "weighted_sum", "state_sharing_enabled": true}, "reward_config": {"return_weight": 1.0, "risk_penalty_weight": 0.5, "transaction_cost_penalty": 1.0, "drawdown_penalty_weight": 2.0, "sharpe_bonus_weight": 0.3, "diversification_bonus": 0.1}}, "system": {"device": "cuda", "mixed_precision": true, "num_workers": 4, "logging": {"level": "INFO", "save_frequency": 100, "log_frequency": 10, "tensorboard_enabled": true, "wandb_enabled": false, "experiment_name": "sota_rl_trading"}, "paths": {"data_dir": "./data", "model_dir": "./checkpoints", "log_dir": "./logs", "result_dir": "./results", "config_dir": "./config"}, "random_seed": 42, "deterministic": true}}