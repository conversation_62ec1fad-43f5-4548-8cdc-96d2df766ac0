"""
SOTA深度强化学习多因子股票投资系统 - 配置文件
整合CNN-LSTM-DQN、动态分块、Transformer注意力机制和层次化强化学习

作者: AI Assistant
版本: 1.0.0
日期: 2025-07-20
"""

import os
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Tuple
import numpy as np

@dataclass
class ModelConfig:
    """模型架构配置"""
    
    # === 层次化动态注意力网络 (H-DAN) 配置 ===
    hierarchical_stages: int = 2  # 层次化阶段数
    dynamic_chunking_enabled: bool = True  # 启用动态分块
    
    # CNN特征提取器配置
    cnn_config: Dict[str, Any] = field(default_factory=lambda: {
        'input_channels': 12,  # 输入通道数（对应因子数量）
        'conv_layers': [
            {'out_channels': 32, 'kernel_size': 3, 'stride': 1, 'padding': 1},
            {'out_channels': 64, 'kernel_size': 3, 'stride': 1, 'padding': 1},
            {'out_channels': 128, 'kernel_size': 3, 'stride': 2, 'padding': 1},
            {'out_channels': 256, 'kernel_size': 3, 'stride': 2, 'padding': 1},
        ],
        'activation': 'LeakyReLU',
        'dropout_rate': 0.2,
        'batch_norm': False,  # 基于论文发现，移除批归一化
        'output_features': 8
    })
    
    # LSTM时序建模配置
    lstm_config: Dict[str, Any] = field(default_factory=lambda: {
        'hidden_size': 256,
        'num_layers': 2,
        'dropout': 0.2,
        'bidirectional': False,
        'attention_enabled': True,  # Bahdanau注意力机制
        'attention_dim': 128,
        'output_features': 8
    })
    
    # Transformer注意力配置
    transformer_config: Dict[str, Any] = field(default_factory=lambda: {
        'd_model': 512,
        'nhead': 8,
        'num_encoder_layers': 6,
        'num_decoder_layers': 6,
        'dim_feedforward': 2048,
        'dropout': 0.1,
        'activation': 'gelu',
        'layer_norm_eps': 1e-5,
        'batch_first': True,
        'norm_first': True
    })
    
    # 动态分块配置（基于第二篇论文）
    dynamic_chunking_config: Dict[str, Any] = field(default_factory=lambda: {
        'target_compression_ratios': [3, 3],  # 每个阶段的压缩比
        'similarity_threshold': 0.5,
        'smoothing_enabled': True,
        'ratio_loss_weight': 0.03,
        'boundary_confidence_threshold': 0.8
    })
    
    # DQN配置
    dqn_config: Dict[str, Any] = field(default_factory=lambda: {
        'state_dim': 17,  # CNN(8) + LSTM(8) + position(1)
        'action_dim': 3,  # Hold, Sell, Buy
        'hidden_layers': [512, 256, 128, 64],
        'activation': 'LeakyReLU',
        'dropout_rate': 0.3,
        'dueling_enabled': True,  # Dueling DQN
        'double_dqn_enabled': True,  # Double DQN
        'noisy_nets_enabled': False,  # Noisy Networks
        'prioritized_replay_enabled': True  # 优先经验回放
    })

@dataclass
class FactorConfig:
    """因子配置"""
    
    # 基础价格因子
    price_factors: List[str] = field(default_factory=lambda: [
        'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount'
    ])
    
    # 技术指标因子
    technical_factors: List[str] = field(default_factory=lambda: [
        'momentum_5d', 'ma_ratio_5d', 'volatility_5d',
        'momentum_10d', 'ma_ratio_10d', 'volatility_10d', 
        'momentum_20d', 'ma_ratio_20d', 'volatility_20d',
        'macd', 'macd_signal', 'macd_hist',
        'bb_ratio', 'atr', 'adaptive_ma'
    ])
    
    # 因子选择配置
    factor_selection: Dict[str, Any] = field(default_factory=lambda: {
        'auto_selection_enabled': True,  # 自动因子选择
        'selection_method': 'attention_based',  # 基于注意力权重选择
        'max_factors': 15,  # 最大因子数量
        'min_importance_threshold': 0.05,  # 最小重要性阈值
        'correlation_threshold': 0.8,  # 相关性阈值
        'stability_window': 252  # 稳定性检验窗口
    })
    
    # 因子预处理配置
    preprocessing: Dict[str, Any] = field(default_factory=lambda: {
        'normalization_method': 'robust_scaler',  # robust_scaler, standard_scaler, min_max
        'outlier_treatment': 'winsorize',  # winsorize, clip, remove
        'winsorize_limits': (0.01, 0.99),
        'missing_value_method': 'forward_fill',  # forward_fill, interpolate, drop
        'feature_engineering_enabled': True
    })

@dataclass
class TradingConfig:
    """交易配置"""
    
    # 基础交易设置
    initial_capital: float = 200000.0  # 初始资金
    max_position_size: float = 0.95  # 最大仓位
    min_position_size: float = 0.05  # 最小仓位
    transaction_cost: float = 0.001  # 交易成本
    slippage: float = 0.0005  # 滑点
    
    # 交易执行设置
    execution_price: str = 'close'  # 执行价格: open, close, vwap
    execution_timing: str = 'close'  # 执行时机: open, close
    allow_short_selling: bool = False  # 允许做空
    
    # 风险管理设置
    risk_management: Dict[str, Any] = field(default_factory=lambda: {
        'max_drawdown_threshold': 0.15,  # 最大回撤阈值
        'stop_loss_threshold': 0.05,  # 止损阈值
        'take_profit_threshold': 0.10,  # 止盈阈值
        'var_confidence_level': 0.05,  # VaR置信水平
        'cvar_enabled': True,  # 启用CVaR
        'risk_budget_allocation': True,  # 风险预算分配
        'volatility_target': 0.15  # 目标波动率
    })
    
    # 仓位管理
    position_sizing: Dict[str, Any] = field(default_factory=lambda: {
        'method': 'kelly_criterion',  # kelly_criterion, equal_weight, risk_parity
        'kelly_fraction': 0.25,  # Kelly准则分数
        'rebalance_frequency': 'daily',  # 再平衡频率
        'min_trade_size': 0.01,  # 最小交易规模
        'max_concentration': 0.20  # 最大集中度
    })

@dataclass
class TrainingConfig:
    """训练配置"""
    
    # 数据分割
    train_start_date: str = '2021-01-01'
    train_end_date: str = '2018-12-31'
    val_start_date: str = '2019-01-01'
    val_end_date: str = '2020-12-31'
    test_start_date: str = '2021-01-01'
    test_end_date: str = '2023-12-31'
    
    # 强化学习训练参数
    rl_config: Dict[str, Any] = field(default_factory=lambda: {
        'episodes': 2000,
        'max_steps_per_episode': 252,  # 一年交易日
        'batch_size': 64,
        'learning_rate': 1e-4,
        'gamma': 0.99,  # 折扣因子
        'epsilon_start': 1.0,
        'epsilon_end': 0.01,
        'epsilon_decay': 0.995,
        'target_update_frequency': 100,
        'memory_size': 100000,
        'min_memory_size': 10000,
        'gradient_clip_norm': 1.0
    })
    
    # 优化器配置
    optimizer_config: Dict[str, Any] = field(default_factory=lambda: {
        'type': 'AdamW',
        'lr': 1e-4,
        'weight_decay': 1e-5,
        'betas': (0.9, 0.999),
        'eps': 1e-8,
        'scheduler_type': 'cosine_annealing',
        'scheduler_params': {'T_max': 1000, 'eta_min': 1e-6}
    })
    
    # 学习率调制（基于层次结构）
    lr_modulation: Dict[str, Any] = field(default_factory=lambda: {
        'enabled': True,
        'outer_stage_multiplier': 2.0,  # 外层网络学习率倍数
        'inner_stage_multiplier': 1.0,  # 内层网络学习率倍数
        'adaptive_scaling': True
    })

@dataclass
class EnvironmentConfig:
    """环境配置"""
    
    # 市场环境设置
    market_hours: Tuple[str, str] = ('09:30', '15:00')
    trading_calendar: str = 'NYSE'  # 交易日历
    benchmark: str = 'SPY'  # 基准指数
    
    # 层次化环境配置
    hierarchical_env: Dict[str, Any] = field(default_factory=lambda: {
        'time_scales': ['daily', 'weekly', 'monthly'],  # 时间尺度
        'decision_frequencies': [1, 5, 20],  # 决策频率（天）
        'reward_aggregation': 'weighted_sum',  # 奖励聚合方式
        'state_sharing_enabled': True  # 状态共享
    })
    
    # 奖励函数配置
    reward_config: Dict[str, Any] = field(default_factory=lambda: {
        'return_weight': 1.0,
        'risk_penalty_weight': 0.5,
        'transaction_cost_penalty': 1.0,
        'drawdown_penalty_weight': 2.0,
        'sharpe_bonus_weight': 0.3,
        'diversification_bonus': 0.1
    })

@dataclass
class SystemConfig:
    """系统配置"""
    
    # 设备配置
    device: str = 'cuda'  # cuda, cpu, mps
    mixed_precision: bool = True  # 混合精度训练
    num_workers: int = 4  # 数据加载器工作进程数
    
    # 日志和检查点
    logging: Dict[str, Any] = field(default_factory=lambda: {
        'level': 'INFO',
        'save_frequency': 100,  # 保存频率（episodes）
        'log_frequency': 10,  # 日志频率
        'tensorboard_enabled': True,
        'wandb_enabled': False,
        'experiment_name': 'sota_rl_trading'
    })
    
    # 路径配置
    paths: Dict[str, str] = field(default_factory=lambda: {
        'data_dir': './data',
        'model_dir': './checkpoints',
        'log_dir': './logs',
        'result_dir': './results',
        'config_dir': './config'
    })
    
    # 随机种子
    random_seed: int = 42
    deterministic: bool = True

# 全局配置实例
class Config:
    """全局配置管理器"""
    
    def __init__(self):
        self.model = ModelConfig()
        self.factor = FactorConfig()
        self.trading = TradingConfig()
        self.training = TrainingConfig()
        self.environment = EnvironmentConfig()
        self.system = SystemConfig()
    
    def update_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        for section, values in config_dict.items():
            if hasattr(self, section):
                section_config = getattr(self, section)
                for key, value in values.items():
                    if hasattr(section_config, key):
                        setattr(section_config, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'model': self.model.__dict__,
            'factor': self.factor.__dict__,
            'trading': self.trading.__dict__,
            'training': self.training.__dict__,
            'environment': self.environment.__dict__,
            'system': self.system.__dict__
        }
    
    def save_config(self, filepath: str):
        """保存配置到文件"""
        import json
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    def load_config(self, filepath: str):
        """从文件加载配置"""
        import json
        with open(filepath, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        self.update_from_dict(config_dict)

# 创建全局配置实例
config = Config()

# 配置验证函数
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 验证因子配置
    total_factors = len(config.factor.price_factors) + len(config.factor.technical_factors)
    if total_factors != config.model.cnn_config['input_channels']:
        errors.append(f"因子总数({total_factors})与CNN输入通道数({config.model.cnn_config['input_channels']})不匹配")
    
    # 验证日期配置
    from datetime import datetime
    try:
        train_start = datetime.strptime(config.training.train_start_date, '%Y-%m-%d')
        train_end = datetime.strptime(config.training.train_end_date, '%Y-%m-%d')
        if train_start >= train_end:
            errors.append("训练开始日期必须早于结束日期")
    except ValueError as e:
        errors.append(f"日期格式错误: {e}")
    
    # 验证交易配置
    if config.trading.max_position_size <= config.trading.min_position_size:
        errors.append("最大仓位必须大于最小仓位")
    
    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(errors))
    
    return True

if __name__ == "__main__":
    # 验证配置
    validate_config()
    print("配置验证通过！")
    
    # 保存默认配置
    config.save_config("default_config.json")
    print("默认配置已保存到 default_config.json")
