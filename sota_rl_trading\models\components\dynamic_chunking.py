"""
动态分块模块 - 基于第二篇论文的Dynamic Chunking技术
实现内容感知和上下文相关的数据分割策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional, Dict
import logging

logger = logging.getLogger(__name__)

class RoutingModule(nn.Module):
    """路由模块 - 预测相邻元素之间的边界"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 128):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 查询和键的投影层
        self.query_proj = nn.Linear(input_dim, hidden_dim)
        self.key_proj = nn.Linear(input_dim, hidden_dim)
        
        # 初始化权重
        nn.init.xavier_uniform_(self.query_proj.weight)
        nn.init.xavier_uniform_(self.key_proj.weight)
        
    def forward(self, x: torch.Tensor) -> Tu<PERSON>[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, input_dim]
            
        Returns:
            boundary_probs: 边界概率 [batch_size, seq_len]
            boundary_indicators: 边界指示器 [batch_size, seq_len]
        """
        batch_size, seq_len, _ = x.shape
        
        # 计算查询和键
        queries = self.query_proj(x)  # [batch_size, seq_len, hidden_dim]
        keys = self.key_proj(x)       # [batch_size, seq_len, hidden_dim]
        
        # 计算相邻元素的余弦相似度
        boundary_probs = torch.zeros(batch_size, seq_len, device=x.device)
        
        # 第一个位置默认为边界
        boundary_probs[:, 0] = 1.0
        
        for t in range(1, seq_len):
            # 当前位置的查询和前一位置的键
            q_t = queries[:, t]      # [batch_size, hidden_dim]
            k_t_minus_1 = keys[:, t-1]  # [batch_size, hidden_dim]
            
            # 计算余弦相似度
            q_norm = F.normalize(q_t, p=2, dim=-1)
            k_norm = F.normalize(k_t_minus_1, p=2, dim=-1)
            cosine_sim = torch.sum(q_norm * k_norm, dim=-1)
            
            # 转换为边界概率 (相似度低 -> 边界概率高)
            boundary_probs[:, t] = 0.5 * (1.0 - cosine_sim)
        
        # 生成边界指示器
        boundary_indicators = (boundary_probs >= 0.5).float()
        
        return boundary_probs, boundary_indicators

class Downsampler(nn.Module):
    """下采样器 - 基于边界指示器压缩序列"""
    
    def __init__(self):
        super().__init__()
    
    def forward(self, x: torch.Tensor, boundary_indicators: torch.Tensor, 
                boundary_probs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, input_dim]
            boundary_indicators: 边界指示器 [batch_size, seq_len]
            boundary_probs: 边界概率 [batch_size, seq_len]
            
        Returns:
            compressed_x: 压缩后的张量 [batch_size, compressed_len, input_dim]
            compressed_probs: 压缩后的概率 [batch_size, compressed_len]
        """
        batch_size, seq_len, input_dim = x.shape
        
        # 为每个批次样本单独处理
        compressed_x_list = []
        compressed_probs_list = []
        
        for b in range(batch_size):
            # 获取边界位置
            boundary_mask = boundary_indicators[b] == 1.0
            boundary_indices = torch.nonzero(boundary_mask, as_tuple=True)[0]
            
            if len(boundary_indices) == 0:
                # 如果没有边界，保留第一个元素
                compressed_x_list.append(x[b:b+1, 0:1])
                compressed_probs_list.append(boundary_probs[b:b+1, 0:1])
            else:
                # 选择边界位置的元素
                selected_x = x[b, boundary_indices]  # [num_boundaries, input_dim]
                selected_probs = boundary_probs[b, boundary_indices]  # [num_boundaries]
                
                compressed_x_list.append(selected_x.unsqueeze(0))
                compressed_probs_list.append(selected_probs.unsqueeze(0))
        
        # 填充到相同长度
        max_len = max(tensor.shape[1] for tensor in compressed_x_list)
        
        padded_x = []
        padded_probs = []
        
        for compressed_x, compressed_probs in zip(compressed_x_list, compressed_probs_list):
            current_len = compressed_x.shape[1]
            if current_len < max_len:
                # 零填充
                pad_x = torch.zeros(1, max_len - current_len, input_dim, 
                                  device=x.device, dtype=x.dtype)
                pad_probs = torch.zeros(1, max_len - current_len, 
                                      device=x.device, dtype=boundary_probs.dtype)
                
                compressed_x = torch.cat([compressed_x, pad_x], dim=1)
                compressed_probs = torch.cat([compressed_probs, pad_probs], dim=1)
            
            padded_x.append(compressed_x)
            padded_probs.append(compressed_probs)
        
        # 拼接批次
        final_x = torch.cat(padded_x, dim=0)
        final_probs = torch.cat(padded_probs, dim=0)
        
        return final_x, final_probs

class SmoothingModule(nn.Module):
    """平滑模块 - 将离散分块操作转换为可微分计算"""
    
    def __init__(self):
        super().__init__()
    
    def forward(self, z_hat: torch.Tensor, P: torch.Tensor) -> torch.Tensor:
        """
        指数移动平均平滑
        
        Args:
            z_hat: 输入张量 [batch_size, seq_len, input_dim]
            P: 边界概率 [batch_size, seq_len]
            
        Returns:
            smoothed_z: 平滑后的张量 [batch_size, seq_len, input_dim]
        """
        batch_size, seq_len, input_dim = z_hat.shape
        
        # 初始化输出
        smoothed_z = torch.zeros_like(z_hat)
        
        # 对每个批次样本处理
        for b in range(batch_size):
            smoothed_z[b, 0] = z_hat[b, 0]  # 第一个元素不变
            
            for t in range(1, seq_len):
                # 指数移动平均: z_bar_t = P_t * z_hat_t + (1 - P_t) * z_bar_{t-1}
                p_t = P[b, t].unsqueeze(-1)  # [1]
                smoothed_z[b, t] = p_t * z_hat[b, t] + (1 - p_t) * smoothed_z[b, t-1]
        
        return smoothed_z

class Upsampler(nn.Module):
    """上采样器 - 将压缩表示恢复到原始分辨率"""
    
    def __init__(self):
        super().__init__()
    
    def forward(self, z_bar: torch.Tensor, boundary_indicators: torch.Tensor, 
                boundary_probs: torch.Tensor, original_length: int) -> torch.Tensor:
        """
        前向传播
        
        Args:
            z_bar: 平滑后的压缩张量 [batch_size, compressed_len, input_dim]
            boundary_indicators: 原始边界指示器 [batch_size, original_length]
            boundary_probs: 原始边界概率 [batch_size, original_length]
            original_length: 原始序列长度
            
        Returns:
            upsampled_z: 上采样后的张量 [batch_size, original_length, input_dim]
        """
        batch_size, _, input_dim = z_bar.shape
        
        # 初始化输出
        upsampled_z = torch.zeros(batch_size, original_length, input_dim, 
                                 device=z_bar.device, dtype=z_bar.dtype)
        
        for b in range(batch_size):
            # 获取边界位置
            boundary_mask = boundary_indicators[b] == 1.0
            boundary_indices = torch.nonzero(boundary_mask, as_tuple=True)[0]
            
            if len(boundary_indices) == 0:
                # 如果没有边界，用第一个压缩元素填充
                upsampled_z[b] = z_bar[b, 0].unsqueeze(0).expand(original_length, -1)
                continue
            
            # 为每个原始位置分配对应的压缩表示
            chunk_idx = 0
            for t in range(original_length):
                # 找到当前位置对应的块索引
                while (chunk_idx < len(boundary_indices) - 1 and 
                       t >= boundary_indices[chunk_idx + 1]):
                    chunk_idx += 1
                
                # 计算置信度权重
                p_t = boundary_probs[b, t]
                b_t = boundary_indicators[b, t]
                
                # 置信度评分
                if b_t == 1.0:
                    confidence = p_t
                else:
                    confidence = 1.0 - p_t
                
                # 直通估计器 (STE)
                confidence_ste = confidence + (1.0 - confidence).detach()
                
                # 应用置信度权重
                if chunk_idx < z_bar.shape[1]:
                    upsampled_z[b, t] = confidence_ste * z_bar[b, chunk_idx]
        
        return upsampled_z

class DynamicChunkingLayer(nn.Module):
    """动态分块层 - 完整的分块操作"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 128):
        super().__init__()
        self.routing_module = RoutingModule(input_dim, hidden_dim)
        self.downsampler = Downsampler()
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播 - 分块操作
        
        Args:
            x: 输入张量 [batch_size, seq_len, input_dim]
            
        Returns:
            compressed_x: 压缩后的张量
            boundary_info: 边界信息 (probs, indicators)
        """
        # 路由模块预测边界
        boundary_probs, boundary_indicators = self.routing_module(x)
        
        # 下采样
        compressed_x, compressed_probs = self.downsampler(x, boundary_indicators, boundary_probs)
        
        # 保存边界信息用于解分块
        boundary_info = {
            'boundary_probs': boundary_probs,
            'boundary_indicators': boundary_indicators,
            'original_length': x.shape[1]
        }
        
        return compressed_x, boundary_info

class DynamicDechunkingLayer(nn.Module):
    """动态解分块层 - 完整的解分块操作"""
    
    def __init__(self):
        super().__init__()
        self.smoothing_module = SmoothingModule()
        self.upsampler = Upsampler()
        
    def forward(self, z_hat: torch.Tensor, boundary_info: dict) -> torch.Tensor:
        """
        前向传播 - 解分块操作
        
        Args:
            z_hat: 压缩后的张量 [batch_size, compressed_len, input_dim]
            boundary_info: 边界信息字典
            
        Returns:
            reconstructed_z: 重构的张量 [batch_size, original_length, input_dim]
        """
        # 提取边界信息
        boundary_probs = boundary_info['boundary_probs']
        boundary_indicators = boundary_info['boundary_indicators']
        original_length = boundary_info['original_length']
        
        # 平滑模块
        z_bar = self.smoothing_module(z_hat, boundary_probs[:, :z_hat.shape[1]])
        
        # 上采样
        reconstructed_z = self.upsampler(z_bar, boundary_indicators, 
                                       boundary_probs, original_length)
        
        return reconstructed_z

class RatioLoss(nn.Module):
    """比率损失 - 控制压缩比率"""
    
    def __init__(self, target_ratio: float = 3.0):
        super().__init__()
        self.target_ratio = target_ratio
        
    def forward(self, boundary_probs: torch.Tensor, boundary_indicators: torch.Tensor) -> torch.Tensor:
        """
        计算比率损失
        
        Args:
            boundary_probs: 边界概率 [batch_size, seq_len]
            boundary_indicators: 边界指示器 [batch_size, seq_len]
            
        Returns:
            ratio_loss: 比率损失
        """
        batch_size, seq_len = boundary_probs.shape
        N = self.target_ratio
        
        # 计算实际选择的比例和平均边界概率
        F = boundary_indicators.mean(dim=1)  # [batch_size]
        G = boundary_probs.mean(dim=1)       # [batch_size]
        
        # 比率损失公式
        ratio_loss = (N / (N - 1)) * ((N - 1) * F * G + (1 - F) * (1 - G))
        
        return ratio_loss.mean()

if __name__ == "__main__":
    # 测试动态分块模块
    batch_size, seq_len, input_dim = 2, 20, 64
    
    # 创建测试数据
    x = torch.randn(batch_size, seq_len, input_dim)
    
    # 测试分块层
    chunking_layer = DynamicChunkingLayer(input_dim)
    compressed_x, boundary_info = chunking_layer(x)
    
    print(f"原始形状: {x.shape}")
    print(f"压缩后形状: {compressed_x.shape}")
    
    # 测试解分块层
    dechunking_layer = DynamicDechunkingLayer()
    reconstructed_x = dechunking_layer(compressed_x, boundary_info)
    
    print(f"重构后形状: {reconstructed_x.shape}")
    
    # 测试比率损失
    ratio_loss_fn = RatioLoss(target_ratio=3.0)
    loss = ratio_loss_fn(boundary_info['boundary_probs'], boundary_info['boundary_indicators'])
    print(f"比率损失: {loss.item()}")
    
    print("动态分块模块测试完成！")
