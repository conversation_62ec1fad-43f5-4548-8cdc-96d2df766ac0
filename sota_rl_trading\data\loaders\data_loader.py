"""
数据加载器 - 支持您提供的CSV格式
处理包含ts_code、trade_date、价格数据和技术指标的股票数据

CSV格式: ts_code, trade_date, open, high, low, close, pct_chg, vol, amount, 
         target_return_close, target_return_high, momentum_5d, ma_ratio_5d, volatility_5d,
         momentum_10d, ma_ratio_10d, volatility_10d, momentum_20d, ma_ratio_20d, volatility_20d,
         macd, macd_signal, macd_hist, bb_ratio, atr, adaptive_ma
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

try:
    from ...config import config
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent))
    from config import config

logger = logging.getLogger(__name__)

class StockDataLoader:
    """股票数据加载器"""
    
    def __init__(self, data_path: Union[str, Path]):
        """
        初始化数据加载器
        
        Args:
            data_path: CSV文件路径
        """
        self.data_path = Path(data_path)
        self.raw_data = None
        self.processed_data = None
        
        # 定义列映射（基础映射，会根据配置的因子动态调整）
        self.base_column_mapping = {
            # 基础信息
            'ts_code': 'stock_code',
            'trade_date': 'date',
        }

        # 动态生成列映射
        self.column_mapping = self.base_column_mapping.copy()

        # 添加用户配置的因子映射
        for factor in config.factor.selected_factors:
            self.column_mapping[factor] = factor
        
        # 因子分类（从配置中获取）
        self.selected_factors = config.factor.selected_factors.copy()
        self.base_price_factors = config.factor._base_price_factors.copy()

        # 分离价格因子和技术因子
        self.price_factors = [f for f in self.selected_factors if f in self.base_price_factors]
        self.technical_factors = [f for f in self.selected_factors if f not in self.base_price_factors]
        
    def load_data(self) -> pd.DataFrame:
        """
        加载CSV数据
        
        Returns:
            加载的原始数据
        """
        try:
            logger.info(f"正在加载数据: {self.data_path}")
            
            # 读取CSV文件，让pandas自动推断日期格式
            self.raw_data = pd.read_csv(
                self.data_path,
                encoding='utf-8',
                parse_dates=['trade_date'],
                date_parser=pd.to_datetime
            )
            
            # 重命名列
            self.raw_data = self.raw_data.rename(columns=self.column_mapping)
            
            # 基础数据验证
            self._validate_data()
            
            logger.info(f"数据加载完成: {len(self.raw_data)} 条记录")
            logger.info(f"日期范围: {self.raw_data['date'].min()} 到 {self.raw_data['date'].max()}")
            logger.info(f"股票数量: {self.raw_data['stock_code'].nunique()}")
            
            return self.raw_data
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def _validate_data(self):
        """验证数据完整性"""
        if self.raw_data is None:
            raise ValueError("数据未加载")

        # 检查必需列
        required_columns = ['stock_code', 'date'] + self.selected_factors
        missing_columns = [col for col in required_columns if col not in self.raw_data.columns]

        if missing_columns:
            raise ValueError(f"缺少必需列: {missing_columns}")

        logger.info(f"数据验证通过，使用因子: {self.selected_factors}")
        
        # 检查数据类型
        if not pd.api.types.is_datetime64_any_dtype(self.raw_data['date']):
            logger.warning("日期列不是datetime类型，尝试转换")
            self.raw_data['date'] = pd.to_datetime(self.raw_data['date'])
        
        # 检查缺失值
        missing_stats = self.raw_data.isnull().sum()
        if missing_stats.sum() > 0:
            logger.warning(f"发现缺失值:\n{missing_stats[missing_stats > 0]}")
        
        # 检查重复记录
        duplicates = self.raw_data.duplicated(subset=['stock_code', 'date']).sum()
        if duplicates > 0:
            logger.warning(f"发现 {duplicates} 条重复记录")
            self.raw_data = self.raw_data.drop_duplicates(subset=['stock_code', 'date'])
    
    def preprocess_data(self) -> pd.DataFrame:
        """
        数据预处理
        
        Returns:
            预处理后的数据
        """
        if self.raw_data is None:
            raise ValueError("请先加载数据")
        
        logger.info("开始数据预处理")
        
        # 复制数据
        self.processed_data = self.raw_data.copy()
        
        # 排序
        self.processed_data = self.processed_data.sort_values(['stock_code', 'date'])
        
        # 处理缺失值
        self._handle_missing_values()
        
        # 异常值处理
        self._handle_outliers()
        
        # 特征工程
        self._feature_engineering()
        
        # 数据标准化
        self._normalize_features()
        
        logger.info("数据预处理完成")
        return self.processed_data
    
    def _handle_missing_values(self):
        """处理缺失值"""
        method = config.factor.preprocessing['missing_value_method']
        
        if method == 'forward_fill':
            # 按股票分组前向填充
            self.processed_data = self.processed_data.groupby('stock_code').fillna(method='ffill')
        elif method == 'interpolate':
            # 线性插值
            numeric_columns = self.processed_data.select_dtypes(include=[np.number]).columns
            self.processed_data[numeric_columns] = self.processed_data.groupby('stock_code')[numeric_columns].apply(
                lambda x: x.interpolate(method='linear')
            )
        elif method == 'drop':
            # 删除含缺失值的行
            self.processed_data = self.processed_data.dropna()
        
        # 剩余缺失值用0填充
        self.processed_data = self.processed_data.fillna(0)
    
    def _handle_outliers(self):
        """处理异常值"""
        method = config.factor.preprocessing['outlier_treatment']
        
        if method == 'winsorize':
            from scipy.stats import mstats
            limits = config.factor.preprocessing['winsorize_limits']

            # 对数值列进行winsorize处理
            numeric_columns = self.selected_factors
            for col in numeric_columns:
                if col in self.processed_data.columns:
                    self.processed_data[col] = mstats.winsorize(
                        self.processed_data[col],
                        limits=limits
                    )
        elif method == 'clip':
            # 使用分位数裁剪
            for col in self.selected_factors:
                if col in self.processed_data.columns:
                    q_low = self.processed_data[col].quantile(0.01)
                    q_high = self.processed_data[col].quantile(0.99)
                    self.processed_data[col] = self.processed_data[col].clip(q_low, q_high)
    
    def _feature_engineering(self):
        """特征工程 - 禁用，只使用用户提供的因子"""
        if not config.factor.preprocessing['feature_engineering_enabled']:
            return

        logger.info("跳过特征工程 - 只使用用户提供的因子")
        # 不进行任何特征工程，只使用CSV文件中已有的因子
    
    def _normalize_features(self):
        """特征标准化"""
        method = config.factor.preprocessing['normalization_method']

        # 需要标准化的列 - 只使用用户配置的因子
        feature_columns = [col for col in self.selected_factors if col in self.processed_data.columns]
        
        if method == 'standard_scaler':
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
            self.processed_data[feature_columns] = scaler.fit_transform(self.processed_data[feature_columns])
        elif method == 'robust_scaler':
            from sklearn.preprocessing import RobustScaler
            scaler = RobustScaler()
            self.processed_data[feature_columns] = scaler.fit_transform(self.processed_data[feature_columns])
        elif method == 'min_max':
            from sklearn.preprocessing import MinMaxScaler
            scaler = MinMaxScaler()
            self.processed_data[feature_columns] = scaler.fit_transform(self.processed_data[feature_columns])
    
    def get_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取特定股票的数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            股票数据
        """
        if self.processed_data is None:
            raise ValueError("请先预处理数据")
        
        # 筛选股票
        stock_data = self.processed_data[self.processed_data['stock_code'] == stock_code].copy()
        
        # 筛选日期
        if start_date:
            stock_data = stock_data[stock_data['date'] >= pd.to_datetime(start_date)]
        if end_date:
            stock_data = stock_data[stock_data['date'] <= pd.to_datetime(end_date)]
        
        return stock_data.sort_values('date')
    
    def get_feature_matrix(self, stock_code: str, lookback_window: int = 60) -> np.ndarray:
        """
        获取特征矩阵用于模型输入

        Args:
            stock_code: 股票代码
            lookback_window: 回看窗口

        Returns:
            特征矩阵 [time_steps, features]
        """
        stock_data = self.get_stock_data(stock_code)

        # 选择特征列 - 只使用用户配置的因子
        feature_columns = [col for col in self.selected_factors if col in stock_data.columns]
        
        # 提取特征
        features = stock_data[feature_columns].values
        
        # 创建滑动窗口
        if len(features) < lookback_window:
            # 如果数据不足，用零填充
            padded_features = np.zeros((lookback_window, features.shape[1]))
            padded_features[-len(features):] = features
            return padded_features
        
        return features[-lookback_window:]
    
    def get_data_summary(self) -> Dict:
        """获取数据摘要统计"""
        if self.processed_data is None:
            raise ValueError("请先预处理数据")
        
        summary = {
            'total_records': len(self.processed_data),
            'unique_stocks': self.processed_data['stock_code'].nunique(),
            'date_range': {
                'start': self.processed_data['date'].min().strftime('%Y-%m-%d'),
                'end': self.processed_data['date'].max().strftime('%Y-%m-%d')
            },
            'missing_values': self.processed_data.isnull().sum().to_dict(),
            'feature_stats': self.processed_data.describe().to_dict()
        }
        
        return summary

# 数据加载器工厂
class DataLoaderFactory:
    """数据加载器工厂"""
    
    @staticmethod
    def create_loader(data_path: Union[str, Path]) -> StockDataLoader:
        """创建数据加载器"""
        return StockDataLoader(data_path)
    
    @staticmethod
    def load_and_preprocess(data_path: Union[str, Path]) -> Tuple[StockDataLoader, pd.DataFrame]:
        """加载并预处理数据"""
        loader = StockDataLoader(data_path)
        loader.load_data()
        processed_data = loader.preprocess_data()
        return loader, processed_data

if __name__ == "__main__":
    # 测试数据加载器
    import sys
    
    if len(sys.argv) > 1:
        data_path = sys.argv[1]
        loader, data = DataLoaderFactory.load_and_preprocess(data_path)
        print("数据加载测试完成")
        print(loader.get_data_summary())
    else:
        print("请提供CSV文件路径作为参数")
