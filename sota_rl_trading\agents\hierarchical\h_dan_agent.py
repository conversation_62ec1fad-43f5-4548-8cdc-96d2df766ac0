"""
层次化动态注意力网络智能体 (H-DAN Agent)
实现基于H-DAN的深度强化学习交易智能体
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import random
from collections import deque, namedtuple
from typing import Dict, List, Tuple, Optional, Any
import logging
import os
from pathlib import Path

from ...models.networks.hierarchical_network import HierarchicalDynamicAttentionNetwork
from ...environments.trading.hierarchical_env import HierarchicalTradingEnvironment
from ...config import config
from ...utils.metrics.performance_metrics import PerformanceMetrics

logger = logging.getLogger(__name__)

# 经验回放缓冲区
Experience = namedtuple('Experience', ['state', 'action', 'reward', 'next_state', 'done', 'auxiliary_info'])

class PrioritizedReplayBuffer:
    """优先经验回放缓冲区"""
    
    def __init__(self, capacity: int, alpha: float = 0.6, beta: float = 0.4):
        self.capacity = capacity
        self.alpha = alpha
        self.beta = beta
        self.buffer = []
        self.priorities = deque(maxlen=capacity)
        self.position = 0
        
    def push(self, experience: Experience, td_error: float = None):
        """添加经验"""
        priority = (abs(td_error) + 1e-6) ** self.alpha if td_error is not None else 1.0
        
        if len(self.buffer) < self.capacity:
            self.buffer.append(experience)
            self.priorities.append(priority)
        else:
            self.buffer[self.position] = experience
            self.priorities[self.position] = priority
            
        self.position = (self.position + 1) % self.capacity
    
    def sample(self, batch_size: int) -> Tuple[List[Experience], np.ndarray, np.ndarray]:
        """采样经验"""
        if len(self.buffer) < batch_size:
            return [], np.array([]), np.array([])
        
        # 计算采样概率
        priorities = np.array(self.priorities)
        probabilities = priorities ** self.alpha
        probabilities /= probabilities.sum()
        
        # 采样索引
        indices = np.random.choice(len(self.buffer), batch_size, p=probabilities)
        
        # 计算重要性权重
        weights = (len(self.buffer) * probabilities[indices]) ** (-self.beta)
        weights /= weights.max()
        
        # 获取经验
        experiences = [self.buffer[idx] for idx in indices]
        
        return experiences, indices, weights
    
    def update_priorities(self, indices: np.ndarray, td_errors: np.ndarray):
        """更新优先级"""
        for idx, td_error in zip(indices, td_errors):
            priority = (abs(td_error) + 1e-6) ** self.alpha
            self.priorities[idx] = priority
    
    def __len__(self):
        return len(self.buffer)

class HDANAgent:
    """H-DAN智能体"""
    
    def __init__(self, device: str = 'cuda'):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.config = config.training.rl_config
        
        # 网络
        self.q_network = HierarchicalDynamicAttentionNetwork().to(self.device)
        self.target_network = HierarchicalDynamicAttentionNetwork().to(self.device)
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        # 优化器
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # 经验回放
        if config.model.dqn_config['prioritized_replay_enabled']:
            self.memory = PrioritizedReplayBuffer(self.config['memory_size'])
        else:
            self.memory = deque(maxlen=self.config['memory_size'])
        
        # 训练参数
        self.epsilon = self.config['epsilon_start']
        self.epsilon_decay = self.config['epsilon_decay']
        self.epsilon_min = self.config['epsilon_end']
        self.gamma = self.config['gamma']
        self.batch_size = self.config['batch_size']
        
        # 统计信息
        self.training_step = 0
        self.episode_rewards = []
        self.episode_losses = []
        self.performance_metrics = PerformanceMetrics()
        
        logger.info(f"H-DAN智能体初始化完成，设备: {self.device}")
    
    def _create_optimizer(self) -> optim.Optimizer:
        """创建优化器"""
        opt_config = config.training.optimizer_config
        
        if opt_config['type'] == 'AdamW':
            optimizer = optim.AdamW(
                self.q_network.parameters(),
                lr=opt_config['lr'],
                weight_decay=opt_config['weight_decay'],
                betas=opt_config['betas'],
                eps=opt_config['eps']
            )
        elif opt_config['type'] == 'Adam':
            optimizer = optim.Adam(
                self.q_network.parameters(),
                lr=opt_config['lr'],
                weight_decay=opt_config['weight_decay']
            )
        else:
            raise ValueError(f"不支持的优化器类型: {opt_config['type']}")
        
        return optimizer
    
    def _create_scheduler(self) -> Optional[optim.lr_scheduler._LRScheduler]:
        """创建学习率调度器"""
        opt_config = config.training.optimizer_config
        
        if opt_config['scheduler_type'] == 'cosine_annealing':
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=opt_config['scheduler_params']['T_max'],
                eta_min=opt_config['scheduler_params']['eta_min']
            )
        elif opt_config['scheduler_type'] == 'step':
            scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=opt_config['scheduler_params'].get('step_size', 100),
                gamma=opt_config['scheduler_params'].get('gamma', 0.9)
            )
        else:
            scheduler = None
        
        return scheduler
    
    def select_action(self, state: np.ndarray, training: bool = True) -> Dict:
        """选择动作"""
        if training and random.random() < self.epsilon:
            # 随机探索
            return self._random_action()
        
        # 贪婪策略
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            
            # 提取特征和位置信息
            features = state_tensor[:, :-1]  # 除了最后一个位置信息
            position = state_tensor[:, -1:] if state_tensor.shape[1] > 0 else torch.zeros(1, 1).to(self.device)
            
            # 重塑特征为序列格式 [batch, seq_len, features]
            seq_len = 60  # 配置的回看窗口
            if features.shape[1] >= seq_len:
                features = features[:, -seq_len:].unsqueeze(1)  # [batch, 1, seq_len]
                features = features.transpose(1, 2)  # [batch, seq_len, 1]
                features = features.expand(-1, -1, features.shape[1])  # 扩展特征维度
            else:
                # 填充到所需长度
                pad_size = seq_len - features.shape[1]
                features = F.pad(features, (0, 0, pad_size, 0))
                features = features.unsqueeze(1).transpose(1, 2)
            
            # 网络前向传播
            q_values, _ = self.q_network(features, position)
            
            # 选择最优动作
            daily_action = q_values.argmax().item()
            
            # 生成层次化动作
            action = {
                'daily_action': daily_action,
                'weekly_action': daily_action,  # 简化：使用相同动作
                'monthly_action': daily_action,
                'position_size': np.array([0.1])  # 固定仓位大小
            }
        
        return action
    
    def _random_action(self) -> Dict:
        """随机动作"""
        return {
            'daily_action': random.randint(0, 2),
            'weekly_action': random.randint(0, 2),
            'monthly_action': random.randint(0, 2),
            'position_size': np.array([random.uniform(0.05, 0.2)])
        }
    
    def store_experience(self, state: np.ndarray, action: Dict, reward: float, 
                        next_state: np.ndarray, done: bool, auxiliary_info: Dict = None):
        """存储经验"""
        experience = Experience(state, action, reward, next_state, done, auxiliary_info)
        
        if isinstance(self.memory, PrioritizedReplayBuffer):
            self.memory.push(experience)
        else:
            self.memory.append(experience)
    
    def train_step(self) -> Dict[str, float]:
        """训练步骤"""
        if len(self.memory) < self.config['min_memory_size']:
            return {}
        
        # 采样经验
        if isinstance(self.memory, PrioritizedReplayBuffer):
            experiences, indices, weights = self.memory.sample(self.batch_size)
            if not experiences:
                return {}
            weights = torch.FloatTensor(weights).to(self.device)
        else:
            experiences = random.sample(self.memory, self.batch_size)
            indices = None
            weights = torch.ones(self.batch_size).to(self.device)
        
        # 解包经验
        states = torch.FloatTensor([e.state for e in experiences]).to(self.device)
        actions = [e.action for e in experiences]
        rewards = torch.FloatTensor([e.reward for e in experiences]).to(self.device)
        next_states = torch.FloatTensor([e.next_state for e in experiences]).to(self.device)
        dones = torch.BoolTensor([e.done for e in experiences]).to(self.device)
        
        # 处理动作
        daily_actions = torch.LongTensor([a['daily_action'] for a in actions]).to(self.device)
        
        # 准备网络输入
        try:
            features, positions = self._prepare_network_input(states)
            next_features, next_positions = self._prepare_network_input(next_states)
        except Exception as e:
            logger.error(f"准备网络输入失败: {e}")
            return {}

        # 当前Q值
        try:
            current_q_values, aux_info = self.q_network(features, positions)
            current_q_values = current_q_values.gather(1, daily_actions.unsqueeze(1))
        except Exception as e:
            logger.error(f"计算当前Q值失败: {e}")
            return {}

        # 目标Q值
        with torch.no_grad():
            try:
                if config.model.dqn_config['double_dqn_enabled']:
                    # Double DQN
                    next_q_values, _ = self.q_network(next_features, next_positions)
                    next_actions = next_q_values.argmax(1)
                    target_next_q_values, _ = self.target_network(next_features, next_positions)
                    target_next_q_values = target_next_q_values.gather(1, next_actions.unsqueeze(1))
                else:
                    # 标准DQN
                    target_next_q_values, _ = self.target_network(next_features, next_positions)
                    target_next_q_values = target_next_q_values.max(1)[0].unsqueeze(1)

                target_q_values = rewards.unsqueeze(1) + (self.gamma * target_next_q_values * ~dones.unsqueeze(1))
            except Exception as e:
                logger.error(f"计算目标Q值失败: {e}")
                return {}
        
        # 计算损失
        td_errors = target_q_values - current_q_values
        main_loss = (weights.unsqueeze(1) * td_errors.pow(2)).mean()
        
        # 辅助损失
        aux_loss = self.q_network.get_auxiliary_loss(aux_info)
        
        # 总损失
        total_loss = main_loss + aux_loss
        
        # 反向传播
        self.optimizer.zero_grad()
        total_loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(
            self.q_network.parameters(), 
            self.config['gradient_clip_norm']
        )
        
        self.optimizer.step()
        
        # 更新优先级
        if isinstance(self.memory, PrioritizedReplayBuffer) and indices is not None:
            td_errors_np = td_errors.detach().cpu().numpy().flatten()
            self.memory.update_priorities(indices, td_errors_np)
        
        # 更新学习率
        if self.scheduler:
            self.scheduler.step()
        
        # 更新epsilon
        self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)
        
        # 更新目标网络
        self.training_step += 1
        if self.training_step % self.config['target_update_frequency'] == 0:
            self.target_network.load_state_dict(self.q_network.state_dict())
        
        return {
            'main_loss': main_loss.item(),
            'aux_loss': aux_loss.item(),
            'total_loss': total_loss.item(),
            'epsilon': self.epsilon,
            'learning_rate': self.optimizer.param_groups[0]['lr']
        }
    
    def _prepare_network_input(self, states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """准备网络输入"""
        batch_size = states.shape[0]
        
        # 分离特征和位置
        features = states[:, :-1]
        positions = states[:, -1:] if states.shape[1] > 1 else torch.zeros(batch_size, 1).to(self.device)
        
        # 重塑特征为序列格式
        seq_len = 60
        feature_dim = features.shape[1] // seq_len if features.shape[1] >= seq_len else 1
        
        if features.shape[1] >= seq_len:
            features = features[:, -seq_len*feature_dim:].view(batch_size, seq_len, feature_dim)
        else:
            # 填充
            pad_size = seq_len - features.shape[1]
            features = F.pad(features, (0, 0, 0, pad_size))
            features = features.view(batch_size, seq_len, 1)
        
        return features, positions
    
    def save_model(self, filepath: str):
        """保存模型"""
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'training_step': self.training_step,
            'epsilon': self.epsilon,
            'episode_rewards': self.episode_rewards,
            'episode_losses': self.episode_losses
        }, filepath)
        logger.info(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.training_step = checkpoint['training_step']
        self.epsilon = checkpoint['epsilon']
        self.episode_rewards = checkpoint.get('episode_rewards', [])
        self.episode_losses = checkpoint.get('episode_losses', [])
        
        logger.info(f"模型已从 {filepath} 加载")
    
    def get_training_stats(self) -> Dict:
        """获取训练统计"""
        return {
            'training_step': self.training_step,
            'epsilon': self.epsilon,
            'memory_size': len(self.memory),
            'avg_episode_reward': np.mean(self.episode_rewards[-100:]) if self.episode_rewards else 0,
            'avg_episode_loss': np.mean(self.episode_losses[-100:]) if self.episode_losses else 0,
            'learning_rate': self.optimizer.param_groups[0]['lr']
        }

if __name__ == "__main__":
    # 测试智能体
    agent = HDANAgent()
    
    # 创建测试状态
    test_state = np.random.randn(100)  # 假设100维状态
    
    # 测试动作选择
    action = agent.select_action(test_state)
    print(f"选择的动作: {action}")
    
    # 测试经验存储
    next_state = np.random.randn(100)
    agent.store_experience(test_state, action, 0.1, next_state, False)
    
    print("H-DAN智能体测试完成！")
