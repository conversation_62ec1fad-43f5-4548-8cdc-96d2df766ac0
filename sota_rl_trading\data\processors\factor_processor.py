"""
因子处理器 - 高级因子工程和选择
基于注意力机制的自适应因子选择和多尺度特征提取
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import torch
import torch.nn as nn
import torch.nn.functional as F

from ...config import config

logger = logging.getLogger(__name__)

class FactorProcessor:
    """因子处理器"""
    
    def __init__(self):
        self.factor_importance_scores = {}
        self.selected_factors = []
        self.factor_correlations = None
        self.pca_transformer = None
        self.scaler = StandardScaler()
        
    def compute_factor_importance(self, data: pd.DataFrame, target_col: str = 'target_return_close') -> Dict[str, float]:
        """
        计算因子重要性
        
        Args:
            data: 输入数据
            target_col: 目标列名
            
        Returns:
            因子重要性字典
        """
        logger.info("计算因子重要性")
        
        # 获取特征列
        feature_cols = config.factor.price_factors + config.factor.technical_factors
        feature_cols = [col for col in feature_cols if col in data.columns]
        
        X = data[feature_cols].fillna(0)
        y = data[target_col].fillna(0)
        
        # 方法1: 互信息
        mi_scores = mutual_info_regression(X, y, random_state=42)
        mi_importance = dict(zip(feature_cols, mi_scores))
        
        # 方法2: F统计量
        f_scores, _ = f_regression(X, y)
        f_importance = dict(zip(feature_cols, f_scores))
        
        # 方法3: 相关系数
        corr_scores = np.abs(X.corrwith(y))
        corr_importance = corr_scores.to_dict()
        
        # 综合重要性评分
        importance_scores = {}
        for factor in feature_cols:
            # 标准化各种评分
            mi_norm = mi_importance.get(factor, 0) / (max(mi_importance.values()) + 1e-8)
            f_norm = f_importance.get(factor, 0) / (max(f_importance.values()) + 1e-8)
            corr_norm = corr_importance.get(factor, 0) / (max(corr_importance.values()) + 1e-8)
            
            # 加权平均
            importance_scores[factor] = 0.4 * mi_norm + 0.3 * f_norm + 0.3 * corr_norm
        
        self.factor_importance_scores = importance_scores
        return importance_scores
    
    def select_factors(self, data: pd.DataFrame, target_col: str = 'target_return_close') -> List[str]:
        """
        自动因子选择
        
        Args:
            data: 输入数据
            target_col: 目标列名
            
        Returns:
            选择的因子列表
        """
        if not config.factor.factor_selection['auto_selection_enabled']:
            # 如果未启用自动选择，返回所有因子
            all_factors = config.factor.price_factors + config.factor.technical_factors
            return [f for f in all_factors if f in data.columns]
        
        logger.info("执行自动因子选择")
        
        # 计算因子重要性
        importance_scores = self.compute_factor_importance(data, target_col)
        
        # 按重要性排序
        sorted_factors = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 应用重要性阈值
        min_threshold = config.factor.factor_selection['min_importance_threshold']
        important_factors = [f for f, score in sorted_factors if score >= min_threshold]
        
        # 相关性过滤
        selected_factors = self._correlation_filter(data, important_factors)
        
        # 限制因子数量
        max_factors = config.factor.factor_selection['max_factors']
        if len(selected_factors) > max_factors:
            selected_factors = selected_factors[:max_factors]
        
        self.selected_factors = selected_factors
        logger.info(f"选择了 {len(selected_factors)} 个因子: {selected_factors}")
        
        return selected_factors
    
    def _correlation_filter(self, data: pd.DataFrame, factors: List[str]) -> List[str]:
        """相关性过滤"""
        if len(factors) <= 1:
            return factors
        
        # 计算相关性矩阵
        factor_data = data[factors].fillna(0)
        corr_matrix = factor_data.corr().abs()
        
        # 贪心算法去除高相关性因子
        selected = []
        threshold = config.factor.factor_selection['correlation_threshold']
        
        # 按重要性排序的因子
        importance_order = sorted(factors, 
                                key=lambda x: self.factor_importance_scores.get(x, 0), 
                                reverse=True)
        
        for factor in importance_order:
            # 检查与已选因子的相关性
            is_correlated = False
            for selected_factor in selected:
                if corr_matrix.loc[factor, selected_factor] > threshold:
                    is_correlated = True
                    break
            
            if not is_correlated:
                selected.append(factor)
        
        return selected
    
    def create_multi_scale_features(self, data: pd.DataFrame, windows: List[int] = [5, 10, 20, 60]) -> pd.DataFrame:
        """
        创建多尺度特征
        
        Args:
            data: 输入数据
            windows: 时间窗口列表
            
        Returns:
            包含多尺度特征的数据
        """
        logger.info("创建多尺度特征")
        
        enhanced_data = data.copy()
        
        # 按股票分组处理
        def create_features_for_stock(group):
            for window in windows:
                # 移动平均
                group[f'ma_{window}'] = group['close'].rolling(window).mean()
                group[f'ma_ratio_{window}'] = group['close'] / group[f'ma_{window}']
                
                # 动量
                group[f'momentum_{window}'] = group['close'].pct_change(window)
                
                # 波动率
                group[f'volatility_{window}'] = group['close'].pct_change().rolling(window).std()
                
                # 最高最低价比率
                group[f'high_low_ratio_{window}'] = (group['high'].rolling(window).max() - 
                                                   group['low'].rolling(window).min()) / group['close']
                
                # 成交量特征
                group[f'volume_ma_{window}'] = group['volume'].rolling(window).mean()
                group[f'volume_ratio_{window}'] = group['volume'] / group[f'volume_ma_{window}']
                
                # 价格位置
                rolling_high = group['high'].rolling(window).max()
                rolling_low = group['low'].rolling(window).min()
                group[f'price_position_{window}'] = ((group['close'] - rolling_low) / 
                                                   (rolling_high - rolling_low + 1e-8))
            
            return group
        
        enhanced_data = enhanced_data.groupby('stock_code').apply(create_features_for_stock)
        enhanced_data = enhanced_data.reset_index(drop=True)
        
        return enhanced_data
    
    def create_interaction_features(self, data: pd.DataFrame, selected_factors: List[str]) -> pd.DataFrame:
        """
        创建交互特征
        
        Args:
            data: 输入数据
            selected_factors: 选择的因子列表
            
        Returns:
            包含交互特征的数据
        """
        logger.info("创建交互特征")
        
        enhanced_data = data.copy()
        
        # 创建重要因子的交互项
        important_factors = selected_factors[:5]  # 取前5个重要因子
        
        for i, factor1 in enumerate(important_factors):
            for factor2 in important_factors[i+1:]:
                if factor1 in data.columns and factor2 in data.columns:
                    # 乘积交互
                    enhanced_data[f'{factor1}_x_{factor2}'] = data[factor1] * data[factor2]
                    
                    # 比率交互
                    enhanced_data[f'{factor1}_div_{factor2}'] = (data[factor1] / 
                                                               (data[factor2].abs() + 1e-8))
        
        return enhanced_data
    
    def apply_pca_reduction(self, data: pd.DataFrame, factors: List[str], 
                          n_components: float = 0.95) -> Tuple[pd.DataFrame, np.ndarray]:
        """
        应用PCA降维
        
        Args:
            data: 输入数据
            factors: 因子列表
            n_components: 保留的方差比例
            
        Returns:
            降维后的数据和主成分
        """
        logger.info("应用PCA降维")
        
        # 提取因子数据
        factor_data = data[factors].fillna(0)
        
        # 标准化
        scaled_data = self.scaler.fit_transform(factor_data)
        
        # PCA
        self.pca_transformer = PCA(n_components=n_components, random_state=42)
        pca_features = self.pca_transformer.fit_transform(scaled_data)
        
        # 创建PCA特征列名
        pca_columns = [f'pca_{i}' for i in range(pca_features.shape[1])]
        
        # 添加到原数据
        enhanced_data = data.copy()
        for i, col in enumerate(pca_columns):
            enhanced_data[col] = pca_features[:, i]
        
        logger.info(f"PCA降维完成: {len(factors)} -> {len(pca_columns)} 维")
        logger.info(f"解释方差比: {self.pca_transformer.explained_variance_ratio_.sum():.3f}")
        
        return enhanced_data, pca_features
    
    def create_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        创建高级技术指标
        
        Args:
            data: 输入数据
            
        Returns:
            包含技术指标的数据
        """
        logger.info("创建高级技术指标")
        
        enhanced_data = data.copy()
        
        def add_technical_indicators(group):
            # RSI
            delta = group['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / (loss + 1e-8)
            group['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD (如果不存在)
            if 'macd' not in group.columns:
                exp1 = group['close'].ewm(span=12).mean()
                exp2 = group['close'].ewm(span=26).mean()
                group['macd'] = exp1 - exp2
                group['macd_signal'] = group['macd'].ewm(span=9).mean()
                group['macd_hist'] = group['macd'] - group['macd_signal']
            
            # 布林带
            ma20 = group['close'].rolling(20).mean()
            std20 = group['close'].rolling(20).std()
            group['bb_upper'] = ma20 + 2 * std20
            group['bb_lower'] = ma20 - 2 * std20
            group['bb_width'] = (group['bb_upper'] - group['bb_lower']) / ma20
            
            # 威廉指标
            high_14 = group['high'].rolling(14).max()
            low_14 = group['low'].rolling(14).min()
            group['williams_r'] = -100 * (high_14 - group['close']) / (high_14 - low_14 + 1e-8)
            
            # 随机指标
            group['stoch_k'] = 100 * (group['close'] - low_14) / (high_14 - low_14 + 1e-8)
            group['stoch_d'] = group['stoch_k'].rolling(3).mean()
            
            # CCI
            tp = (group['high'] + group['low'] + group['close']) / 3
            ma_tp = tp.rolling(20).mean()
            mad = tp.rolling(20).apply(lambda x: np.mean(np.abs(x - x.mean())))
            group['cci'] = (tp - ma_tp) / (0.015 * mad + 1e-8)
            
            return group
        
        enhanced_data = enhanced_data.groupby('stock_code').apply(add_technical_indicators)
        enhanced_data = enhanced_data.reset_index(drop=True)
        
        return enhanced_data
    
    def process_factors(self, data: pd.DataFrame, target_col: str = 'target_return_close') -> pd.DataFrame:
        """
        完整的因子处理流程
        
        Args:
            data: 输入数据
            target_col: 目标列名
            
        Returns:
            处理后的数据
        """
        logger.info("开始因子处理流程")
        
        # 1. 创建多尺度特征
        enhanced_data = self.create_multi_scale_features(data)
        
        # 2. 创建技术指标
        enhanced_data = self.create_technical_indicators(enhanced_data)
        
        # 3. 因子选择
        selected_factors = self.select_factors(enhanced_data, target_col)
        
        # 4. 创建交互特征
        enhanced_data = self.create_interaction_features(enhanced_data, selected_factors)
        
        # 5. 更新选择的因子列表（包含新创建的特征）
        all_new_factors = [col for col in enhanced_data.columns 
                          if col not in data.columns and col != target_col]
        final_factors = selected_factors + all_new_factors
        
        logger.info(f"因子处理完成，总计 {len(final_factors)} 个因子")
        
        return enhanced_data
    
    def get_factor_summary(self) -> Dict:
        """获取因子处理摘要"""
        return {
            'selected_factors': self.selected_factors,
            'factor_importance': self.factor_importance_scores,
            'num_selected_factors': len(self.selected_factors),
            'pca_components': self.pca_transformer.n_components_ if self.pca_transformer else None,
            'explained_variance_ratio': (self.pca_transformer.explained_variance_ratio_.sum() 
                                       if self.pca_transformer else None)
        }

class AttentionBasedFactorSelector(nn.Module):
    """基于注意力机制的因子选择器"""
    
    def __init__(self, num_factors: int, hidden_dim: int = 128):
        super().__init__()
        self.num_factors = num_factors
        self.hidden_dim = hidden_dim
        
        # 注意力网络
        self.attention_net = nn.Sequential(
            nn.Linear(num_factors, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, num_factors),
            nn.Softmax(dim=-1)
        )
        
    def forward(self, factors: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            factors: 因子张量 [batch_size, num_factors]
            
        Returns:
            加权因子和注意力权重
        """
        # 计算注意力权重
        attention_weights = self.attention_net(factors)
        
        # 应用注意力权重
        weighted_factors = factors * attention_weights
        
        return weighted_factors, attention_weights
    
    def get_factor_importance(self, factors: torch.Tensor) -> torch.Tensor:
        """获取因子重要性"""
        with torch.no_grad():
            _, attention_weights = self.forward(factors)
            return attention_weights.mean(dim=0)

if __name__ == "__main__":
    # 测试因子处理器
    processor = FactorProcessor()
    print("因子处理器初始化完成")
