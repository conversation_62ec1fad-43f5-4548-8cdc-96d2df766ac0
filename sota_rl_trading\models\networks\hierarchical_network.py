"""
层次化动态注意力网络 (H-DAN)
整合CNN-LSTM-DQN、Transformer注意力机制和动态分块技术
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Dict, List, Optional
import logging

from ..components.dynamic_chunking import DynamicChunkingLayer, DynamicDechunkingLayer, RatioLoss
from ...config import config

logger = logging.getLogger(__name__)

class CNNFeatureExtractor(nn.Module):
    """CNN特征提取器 - 识别空间模式"""
    
    def __init__(self, config_dict: Dict):
        super().__init__()
        self.config = config_dict
        
        # 构建卷积层
        layers = []
        in_channels = self.config['input_channels']
        
        for i, layer_config in enumerate(self.config['conv_layers']):
            out_channels = layer_config['out_channels']
            kernel_size = layer_config['kernel_size']
            stride = layer_config['stride']
            padding = layer_config['padding']
            
            # 卷积层
            layers.append(nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding))
            
            # 激活函数
            if self.config['activation'] == 'LeakyReLU':
                layers.append(nn.LeakyReLU(0.2, inplace=True))
            elif self.config['activation'] == 'ReLU':
                layers.append(nn.ReLU(inplace=True))
            
            # Dropout
            if self.config['dropout_rate'] > 0:
                layers.append(nn.Dropout2d(self.config['dropout_rate']))
            
            in_channels = out_channels
        
        self.conv_layers = nn.Sequential(*layers)
        
        # 自适应池化
        self.adaptive_pool = nn.AdaptiveAvgPool2d((1, 1))
        
        # 输出投影
        self.output_projection = nn.Linear(in_channels, self.config['output_features'])
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, channels, height, width]
            
        Returns:
            features: 提取的特征 [batch_size, output_features]
        """
        # 卷积特征提取
        features = self.conv_layers(x)
        
        # 全局平均池化
        features = self.adaptive_pool(features)
        features = features.view(features.size(0), -1)
        
        # 输出投影
        features = self.output_projection(features)
        
        return features

class LSTMWithAttention(nn.Module):
    """带注意力机制的LSTM - 捕获时序依赖"""
    
    def __init__(self, config_dict: Dict):
        super().__init__()
        self.config = config_dict
        self.hidden_size = config_dict['hidden_size']
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=config_dict.get('input_size', 81),  # 9x9展平
            hidden_size=self.hidden_size,
            num_layers=config_dict['num_layers'],
            dropout=config_dict['dropout'] if config_dict['num_layers'] > 1 else 0,
            bidirectional=config_dict['bidirectional'],
            batch_first=True
        )
        
        # 注意力机制 (Bahdanau)
        if config_dict['attention_enabled']:
            self.attention_dim = config_dict['attention_dim']
            self.attention_W = nn.Linear(self.hidden_size, self.attention_dim)
            self.attention_U = nn.Linear(self.hidden_size, self.attention_dim)
            self.attention_v = nn.Linear(self.attention_dim, 1)
        
        # 输出投影
        self.output_projection = nn.Linear(self.hidden_size, config_dict['output_features'])
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, input_size]
            
        Returns:
            features: 提取的特征 [batch_size, output_features]
            attention_weights: 注意力权重 [batch_size, seq_len] (如果启用)
        """
        # LSTM前向传播
        lstm_out, (hidden, cell) = self.lstm(x)  # [batch_size, seq_len, hidden_size]
        
        attention_weights = None
        
        if self.config['attention_enabled']:
            # 计算注意力权重
            # 使用最后一个隐藏状态作为查询
            query = hidden[-1]  # [batch_size, hidden_size]
            
            # 计算注意力分数
            scores = []
            for t in range(lstm_out.size(1)):
                # W * h_t + U * s
                score = torch.tanh(
                    self.attention_W(lstm_out[:, t, :]) + 
                    self.attention_U(query)
                )
                score = self.attention_v(score)  # [batch_size, 1]
                scores.append(score)
            
            scores = torch.cat(scores, dim=1)  # [batch_size, seq_len]
            attention_weights = F.softmax(scores, dim=1)
            
            # 应用注意力权重
            context = torch.sum(lstm_out * attention_weights.unsqueeze(-1), dim=1)
        else:
            # 使用最后一个输出
            context = lstm_out[:, -1, :]
        
        # 输出投影
        features = self.output_projection(context)
        
        return features, attention_weights

class TransformerEncoder(nn.Module):
    """Transformer编码器 - 高级注意力机制"""
    
    def __init__(self, config_dict: Dict):
        super().__init__()
        self.config = config_dict
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(
            config_dict['d_model'], 
            max_len=config_dict.get('max_len', 1000)
        )
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=config_dict['d_model'],
            nhead=config_dict['nhead'],
            dim_feedforward=config_dict['dim_feedforward'],
            dropout=config_dict['dropout'],
            activation=config_dict['activation'],
            layer_norm_eps=config_dict['layer_norm_eps'],
            batch_first=config_dict['batch_first'],
            norm_first=config_dict['norm_first']
        )
        
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=config_dict['num_encoder_layers']
        )
        
        # 输出投影
        self.output_projection = nn.Linear(config_dict['d_model'], config_dict.get('output_features', 256))
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, d_model]
            mask: 注意力掩码
            
        Returns:
            features: 编码后的特征 [batch_size, seq_len, output_features]
        """
        # 添加位置编码
        x = self.pos_encoding(x)
        
        # Transformer编码
        encoded = self.transformer_encoder(x, mask=mask)
        
        # 输出投影
        features = self.output_projection(encoded)
        
        return features

class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_len: int = 1000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x + self.pe[:x.size(1), :].transpose(0, 1)

class DuelingDQN(nn.Module):
    """Dueling DQN - 分离状态价值和动作优势"""
    
    def __init__(self, config_dict: Dict):
        super().__init__()
        self.config = config_dict
        
        state_dim = config_dict['state_dim']
        action_dim = config_dict['action_dim']
        hidden_layers = config_dict['hidden_layers']
        
        # 共享特征层
        shared_layers = []
        input_dim = state_dim
        
        for hidden_dim in hidden_layers[:-1]:
            shared_layers.append(nn.Linear(input_dim, hidden_dim))
            
            if config_dict['activation'] == 'LeakyReLU':
                shared_layers.append(nn.LeakyReLU(0.2))
            elif config_dict['activation'] == 'ReLU':
                shared_layers.append(nn.ReLU())
            
            if config_dict['dropout_rate'] > 0:
                shared_layers.append(nn.Dropout(config_dict['dropout_rate']))
            
            input_dim = hidden_dim
        
        self.shared_layers = nn.Sequential(*shared_layers)
        
        # 状态价值流
        self.value_stream = nn.Sequential(
            nn.Linear(input_dim, hidden_layers[-1]),
            nn.LeakyReLU(0.2) if config_dict['activation'] == 'LeakyReLU' else nn.ReLU(),
            nn.Linear(hidden_layers[-1], 1)
        )
        
        # 动作优势流
        self.advantage_stream = nn.Sequential(
            nn.Linear(input_dim, hidden_layers[-1]),
            nn.LeakyReLU(0.2) if config_dict['activation'] == 'LeakyReLU' else nn.ReLU(),
            nn.Linear(hidden_layers[-1], action_dim)
        )
        
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            state: 状态张量 [batch_size, state_dim]
            
        Returns:
            q_values: Q值 [batch_size, action_dim]
        """
        # 共享特征
        shared_features = self.shared_layers(state)
        
        # 状态价值和动作优势
        value = self.value_stream(shared_features)
        advantage = self.advantage_stream(shared_features)
        
        # Dueling架构: Q(s,a) = V(s) + A(s,a) - mean(A(s,a))
        q_values = value + advantage - advantage.mean(dim=1, keepdim=True)
        
        return q_values

class HierarchicalDynamicAttentionNetwork(nn.Module):
    """层次化动态注意力网络 (H-DAN) - 主网络"""
    
    def __init__(self):
        super().__init__()
        
        # 配置
        self.model_config = config.model
        self.hierarchical_stages = self.model_config.hierarchical_stages
        
        # 编码器网络 (外层)
        self.encoders = nn.ModuleList()
        for stage in range(self.hierarchical_stages):
            encoder = nn.ModuleDict({
                'cnn': CNNFeatureExtractor(self.model_config.cnn_config),
                'lstm': LSTMWithAttention(self.model_config.lstm_config),
                'transformer': TransformerEncoder(self.model_config.transformer_config)
            })
            self.encoders.append(encoder)
        
        # 动态分块层
        if self.model_config.dynamic_chunking_enabled:
            self.chunking_layers = nn.ModuleList()
            self.dechunking_layers = nn.ModuleList()
            self.ratio_losses = nn.ModuleList()
            
            for stage in range(self.hierarchical_stages):
                input_dim = self.model_config.transformer_config['d_model']
                self.chunking_layers.append(DynamicChunkingLayer(input_dim))
                self.dechunking_layers.append(DynamicDechunkingLayer())
                
                target_ratio = self.model_config.dynamic_chunking_config['target_compression_ratios'][stage]
                self.ratio_losses.append(RatioLoss(target_ratio))
        
        # 主网络 (内层)
        self.main_network = DuelingDQN(self.model_config.dqn_config)
        
        # 解码器网络
        self.decoders = nn.ModuleList()
        for stage in range(self.hierarchical_stages):
            decoder = TransformerEncoder(self.model_config.transformer_config)
            self.decoders.append(decoder)
        
        # 残差连接投影
        self.residual_projections = nn.ModuleList()
        for stage in range(self.hierarchical_stages):
            proj = nn.Linear(
                self.model_config.transformer_config['d_model'],
                self.model_config.transformer_config['d_model']
            )
            self.residual_projections.append(proj)
        
        # 网络归一化
        self.network_norms = nn.ModuleList()
        for stage in range(self.hierarchical_stages + 1):  # +1 for main network
            self.network_norms.append(nn.RMSNorm(self.model_config.transformer_config['d_model']))
    
    def forward(self, x: torch.Tensor, position: torch.Tensor) -> Tuple[torch.Tensor, Dict]:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, features]
            position: 当前持仓状态 [batch_size, 1]
            
        Returns:
            q_values: Q值 [batch_size, action_dim]
            auxiliary_info: 辅助信息字典
        """
        auxiliary_info = {
            'attention_weights': [],
            'boundary_info': [],
            'ratio_losses': []
        }
        
        # 层次化编码
        encoded_features = []
        current_x = x
        
        for stage in range(self.hierarchical_stages):
            # 编码器处理
            encoder = self.encoders[stage]
            
            # CNN特征提取 (如果输入是图像格式)
            if len(current_x.shape) == 4:  # [batch, channel, height, width]
                cnn_features = encoder['cnn'](current_x)
                encoded_features.append(cnn_features)
            
            # LSTM时序建模
            if len(current_x.shape) == 3:  # [batch, seq, features]
                lstm_features, attention_weights = encoder['lstm'](current_x)
                auxiliary_info['attention_weights'].append(attention_weights)
                
                # Transformer编码
                transformer_input = current_x
                if transformer_input.size(-1) != self.model_config.transformer_config['d_model']:
                    # 维度投影
                    proj = nn.Linear(transformer_input.size(-1), 
                                   self.model_config.transformer_config['d_model']).to(current_x.device)
                    transformer_input = proj(transformer_input)
                
                transformer_features = encoder['transformer'](transformer_input)
                
                # 网络归一化
                transformer_features = self.network_norms[stage](transformer_features)
                
                # 动态分块
                if self.model_config.dynamic_chunking_enabled:
                    chunked_features, boundary_info = self.chunking_layers[stage](transformer_features)
                    auxiliary_info['boundary_info'].append(boundary_info)
                    
                    # 计算比率损失
                    ratio_loss = self.ratio_losses[stage](
                        boundary_info['boundary_probs'],
                        boundary_info['boundary_indicators']
                    )
                    auxiliary_info['ratio_losses'].append(ratio_loss)
                    
                    current_x = chunked_features
                else:
                    current_x = transformer_features
                
                encoded_features.append(current_x)
        
        # 主网络处理
        # 聚合所有编码特征
        if len(encoded_features) > 1:
            # 特征融合
            main_input = torch.cat([f.mean(dim=1) if len(f.shape) == 3 else f 
                                  for f in encoded_features], dim=-1)
        else:
            main_input = encoded_features[0].mean(dim=1) if len(encoded_features[0].shape) == 3 else encoded_features[0]
        
        # 添加持仓信息
        state_vector = torch.cat([main_input, position], dim=-1)
        
        # 主网络归一化
        if state_vector.size(-1) == self.model_config.transformer_config['d_model']:
            state_vector = self.network_norms[-1](state_vector)
        
        # DQN决策
        q_values = self.main_network(state_vector)
        
        return q_values, auxiliary_info
    
    def get_auxiliary_loss(self, auxiliary_info: Dict) -> torch.Tensor:
        """计算辅助损失"""
        total_loss = 0.0
        
        # 比率损失
        if auxiliary_info['ratio_losses']:
            ratio_loss_weight = self.model_config.dynamic_chunking_config['ratio_loss_weight']
            for ratio_loss in auxiliary_info['ratio_losses']:
                total_loss += ratio_loss_weight * ratio_loss
        
        return total_loss

if __name__ == "__main__":
    # 测试网络
    model = HierarchicalDynamicAttentionNetwork()
    
    # 创建测试数据
    batch_size = 2
    seq_len = 60
    features = 25  # 因子数量
    
    x = torch.randn(batch_size, seq_len, features)
    position = torch.randn(batch_size, 1)
    
    # 前向传播
    q_values, aux_info = model(x, position)
    
    print(f"输入形状: {x.shape}")
    print(f"Q值形状: {q_values.shape}")
    print(f"辅助损失: {model.get_auxiliary_loss(aux_info)}")
    
    print("H-DAN网络测试完成！")
