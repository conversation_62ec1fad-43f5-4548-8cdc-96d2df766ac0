"""
SOTA深度强化学习多因子股票投资系统

一个超越当前SOTA水平的深度强化学习多因子股票投资系统，
整合了CNN-LSTM-DQN、动态分块、Transformer注意力机制和层次化强化学习技术。

主要特性:
- 层次化动态注意力网络 (H-DAN)
- 自适应因子选择和多尺度特征提取
- 风险感知交易系统
- 专业级性能评估和可视化

版本: 1.0.0
作者: AI Assistant
日期: 2025-07-20
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"
__email__ = "<EMAIL>"
__description__ = "SOTA深度强化学习多因子股票投资系统"

# 导入主要模块
from .config import config, validate_config
from .data.loaders.data_loader import DataLoaderFactory
from .data.processors.factor_processor import FactorProcessor
from .environments.trading.hierarchical_env import HierarchicalTradingEnvironment
from .agents.hierarchical.h_dan_agent import HDANAgent
from .utils.metrics.performance_metrics import PerformanceMetrics
from .utils.risk.risk_manager import RiskManager
from .visualization.training_monitor import TrainingMonitor

__all__ = [
    'config',
    'validate_config',
    'DataLoaderFactory',
    'FactorProcessor', 
    'HierarchicalTradingEnvironment',
    'HDANAgent',
    'PerformanceMetrics',
    'RiskManager',
    'TrainingMonitor'
]
