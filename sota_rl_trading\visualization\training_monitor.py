"""
训练监控器 - 实时监控训练过程和可视化
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

logger = logging.getLogger(__name__)

class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self, save_dir: str = 'visualization/plots'):
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # 训练历史
        self.episodes = []
        self.rewards = []
        self.losses = []
        self.epsilons = []
        self.learning_rates = []
        
        # 性能指标历史
        self.performance_history = []
        
        # 设置绘图样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def update(self, episode: int, reward: float, loss: float, 
               epsilon: float, learning_rate: float = None):
        """更新训练指标"""
        self.episodes.append(episode)
        self.rewards.append(reward)
        self.losses.append(loss)
        self.epsilons.append(epsilon)
        
        if learning_rate is not None:
            self.learning_rates.append(learning_rate)
    
    def plot_training_curves(self, stock_code: str = None, save: bool = True) -> go.Figure:
        """绘制训练曲线"""
        if not self.episodes:
            logger.warning("没有训练数据可绘制")
            return None
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('奖励曲线', '损失曲线', 'Epsilon衰减', '学习率变化'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 奖励曲线
        fig.add_trace(
            go.Scatter(x=self.episodes, y=self.rewards, name='奖励', 
                      line=dict(color='blue', width=2)),
            row=1, col=1
        )
        
        # 添加移动平均
        if len(self.rewards) > 10:
            ma_rewards = pd.Series(self.rewards).rolling(window=10).mean()
            fig.add_trace(
                go.Scatter(x=self.episodes, y=ma_rewards, name='奖励(MA10)',
                          line=dict(color='red', width=2, dash='dash')),
                row=1, col=1
            )
        
        # 损失曲线
        fig.add_trace(
            go.Scatter(x=self.episodes, y=self.losses, name='损失',
                      line=dict(color='orange', width=2)),
            row=1, col=2
        )
        
        # Epsilon衰减
        fig.add_trace(
            go.Scatter(x=self.episodes, y=self.epsilons, name='Epsilon',
                      line=dict(color='green', width=2)),
            row=2, col=1
        )
        
        # 学习率变化
        if self.learning_rates:
            fig.add_trace(
                go.Scatter(x=self.episodes, y=self.learning_rates, name='学习率',
                          line=dict(color='purple', width=2)),
                row=2, col=2
            )
        
        # 更新布局
        title = f'训练监控 - {stock_code}' if stock_code else '训练监控'
        fig.update_layout(
            title=title,
            height=800,
            showlegend=True,
            template='plotly_white'
        )
        
        # 更新坐标轴标签
        fig.update_xaxes(title_text="Episode", row=1, col=1)
        fig.update_xaxes(title_text="Episode", row=1, col=2)
        fig.update_xaxes(title_text="Episode", row=2, col=1)
        fig.update_xaxes(title_text="Episode", row=2, col=2)
        
        fig.update_yaxes(title_text="奖励", row=1, col=1)
        fig.update_yaxes(title_text="损失", row=1, col=2)
        fig.update_yaxes(title_text="Epsilon", row=2, col=1)
        fig.update_yaxes(title_text="学习率", row=2, col=2)
        
        if save:
            filename = f"training_curves_{stock_code}.html" if stock_code else "training_curves.html"
            fig.write_html(self.save_dir / filename)
            logger.info(f"训练曲线已保存: {self.save_dir / filename}")
        
        return fig
    
    def plot_attention_weights(self, attention_weights: np.ndarray, 
                              factor_names: List[str], 
                              stock_code: str = None, save: bool = True) -> go.Figure:
        """绘制注意力权重热力图"""
        if attention_weights is None or len(attention_weights) == 0:
            logger.warning("没有注意力权重数据可绘制")
            return None
        
        # 创建热力图
        fig = go.Figure(data=go.Heatmap(
            z=attention_weights,
            x=factor_names,
            y=[f'时间步{i}' for i in range(attention_weights.shape[0])],
            colorscale='Viridis',
            colorbar=dict(title="注意力权重")
        ))
        
        title = f'注意力权重热力图 - {stock_code}' if stock_code else '注意力权重热力图'
        fig.update_layout(
            title=title,
            xaxis_title="因子",
            yaxis_title="时间步",
            height=600,
            template='plotly_white'
        )
        
        if save:
            filename = f"attention_weights_{stock_code}.html" if stock_code else "attention_weights.html"
            fig.write_html(self.save_dir / filename)
            logger.info(f"注意力权重图已保存: {self.save_dir / filename}")
        
        return fig
    
    def plot_factor_importance(self, importance_scores: Dict[str, float],
                              stock_code: str = None, save: bool = True) -> go.Figure:
        """绘制因子重要性"""
        if not importance_scores:
            logger.warning("没有因子重要性数据可绘制")
            return None
        
        # 排序因子
        sorted_factors = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)
        factors, scores = zip(*sorted_factors)
        
        # 创建条形图
        fig = go.Figure(data=go.Bar(
            x=list(factors),
            y=list(scores),
            marker_color='lightblue',
            text=[f'{score:.3f}' for score in scores],
            textposition='auto'
        ))
        
        title = f'因子重要性 - {stock_code}' if stock_code else '因子重要性'
        fig.update_layout(
            title=title,
            xaxis_title="因子",
            yaxis_title="重要性得分",
            height=600,
            template='plotly_white',
            xaxis_tickangle=-45
        )
        
        if save:
            filename = f"factor_importance_{stock_code}.html" if stock_code else "factor_importance.html"
            fig.write_html(self.save_dir / filename)
            logger.info(f"因子重要性图已保存: {self.save_dir / filename}")
        
        return fig
    
    def plot_portfolio_performance(self, portfolio_values: List[float],
                                  benchmark_values: List[float] = None,
                                  dates: List[str] = None,
                                  stock_code: str = None, save: bool = True) -> go.Figure:
        """绘制投资组合性能"""
        if not portfolio_values:
            logger.warning("没有投资组合数据可绘制")
            return None
        
        # 准备x轴
        x_axis = dates if dates else list(range(len(portfolio_values)))
        
        fig = go.Figure()
        
        # 投资组合曲线
        fig.add_trace(go.Scatter(
            x=x_axis,
            y=portfolio_values,
            name='投资组合',
            line=dict(color='blue', width=2)
        ))
        
        # 基准曲线
        if benchmark_values:
            fig.add_trace(go.Scatter(
                x=x_axis,
                y=benchmark_values,
                name='基准',
                line=dict(color='red', width=2, dash='dash')
            ))
        
        title = f'投资组合性能 - {stock_code}' if stock_code else '投资组合性能'
        fig.update_layout(
            title=title,
            xaxis_title="时间" if dates else "交易日",
            yaxis_title="组合价值",
            height=600,
            template='plotly_white',
            hovermode='x unified'
        )
        
        if save:
            filename = f"portfolio_performance_{stock_code}.html" if stock_code else "portfolio_performance.html"
            fig.write_html(self.save_dir / filename)
            logger.info(f"投资组合性能图已保存: {self.save_dir / filename}")
        
        return fig
    
    def plot_drawdown_analysis(self, portfolio_values: List[float],
                              dates: List[str] = None,
                              stock_code: str = None, save: bool = True) -> go.Figure:
        """绘制回撤分析"""
        if not portfolio_values:
            logger.warning("没有投资组合数据可绘制")
            return None
        
        # 计算回撤
        values = np.array(portfolio_values)
        peak = np.maximum.accumulate(values)
        drawdown = (peak - values) / peak * 100
        
        x_axis = dates if dates else list(range(len(portfolio_values)))
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('投资组合价值', '回撤 (%)'),
            shared_xaxes=True,
            vertical_spacing=0.1
        )
        
        # 投资组合价值
        fig.add_trace(
            go.Scatter(x=x_axis, y=portfolio_values, name='投资组合价值',
                      line=dict(color='blue', width=2)),
            row=1, col=1
        )
        
        # 回撤
        fig.add_trace(
            go.Scatter(x=x_axis, y=-drawdown, name='回撤',
                      fill='tonexty', fillcolor='rgba(255,0,0,0.3)',
                      line=dict(color='red', width=1)),
            row=2, col=1
        )
        
        title = f'回撤分析 - {stock_code}' if stock_code else '回撤分析'
        fig.update_layout(
            title=title,
            height=800,
            template='plotly_white',
            showlegend=True
        )
        
        fig.update_xaxes(title_text="时间" if dates else "交易日", row=2, col=1)
        fig.update_yaxes(title_text="价值", row=1, col=1)
        fig.update_yaxes(title_text="回撤 (%)", row=2, col=1)
        
        if save:
            filename = f"drawdown_analysis_{stock_code}.html" if stock_code else "drawdown_analysis.html"
            fig.write_html(self.save_dir / filename)
            logger.info(f"回撤分析图已保存: {self.save_dir / filename}")
        
        return fig
    
    def plot_trading_actions(self, actions: List[int], prices: List[float],
                           dates: List[str] = None,
                           stock_code: str = None, save: bool = True) -> go.Figure:
        """绘制交易动作"""
        if not actions or not prices:
            logger.warning("没有交易动作数据可绘制")
            return None
        
        x_axis = dates if dates else list(range(len(prices)))
        
        fig = go.Figure()
        
        # 价格曲线
        fig.add_trace(go.Scatter(
            x=x_axis,
            y=prices,
            name='价格',
            line=dict(color='black', width=1)
        ))
        
        # 买入点
        buy_indices = [i for i, action in enumerate(actions) if action == 2]
        if buy_indices:
            fig.add_trace(go.Scatter(
                x=[x_axis[i] for i in buy_indices],
                y=[prices[i] for i in buy_indices],
                mode='markers',
                name='买入',
                marker=dict(color='green', size=10, symbol='triangle-up')
            ))
        
        # 卖出点
        sell_indices = [i for i, action in enumerate(actions) if action == 1]
        if sell_indices:
            fig.add_trace(go.Scatter(
                x=[x_axis[i] for i in sell_indices],
                y=[prices[i] for i in sell_indices],
                mode='markers',
                name='卖出',
                marker=dict(color='red', size=10, symbol='triangle-down')
            ))
        
        title = f'交易动作分析 - {stock_code}' if stock_code else '交易动作分析'
        fig.update_layout(
            title=title,
            xaxis_title="时间" if dates else "交易日",
            yaxis_title="价格",
            height=600,
            template='plotly_white',
            hovermode='x unified'
        )
        
        if save:
            filename = f"trading_actions_{stock_code}.html" if stock_code else "trading_actions.html"
            fig.write_html(self.save_dir / filename)
            logger.info(f"交易动作图已保存: {self.save_dir / filename}")
        
        return fig
    
    def generate_dashboard(self, stock_code: str, results: Dict) -> str:
        """生成综合仪表板"""
        dashboard_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>SOTA交易系统仪表板 - {stock_code}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; color: #333; }}
                .metrics {{ display: flex; justify-content: space-around; margin: 20px 0; }}
                .metric {{ text-align: center; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
                .chart-container {{ margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>SOTA深度强化学习多因子股票投资系统</h1>
                <h2>股票代码: {stock_code}</h2>
            </div>
            
            <div class="metrics">
                <div class="metric">
                    <h3>总收益率</h3>
                    <p>{results.get('total_return', 0):.2%}</p>
                </div>
                <div class="metric">
                    <h3>夏普比率</h3>
                    <p>{results.get('sharpe_ratio', 0):.2f}</p>
                </div>
                <div class="metric">
                    <h3>最大回撤</h3>
                    <p>{results.get('max_drawdown', 0):.2%}</p>
                </div>
                <div class="metric">
                    <h3>胜率</h3>
                    <p>{results.get('win_rate', 0):.2%}</p>
                </div>
            </div>
            
            <div class="chart-container">
                <iframe src="training_curves_{stock_code}.html" width="100%" height="800"></iframe>
            </div>
            
            <div class="chart-container">
                <iframe src="portfolio_performance_{stock_code}.html" width="100%" height="600"></iframe>
            </div>
            
            <div class="chart-container">
                <iframe src="drawdown_analysis_{stock_code}.html" width="100%" height="800"></iframe>
            </div>
        </body>
        </html>
        """
        
        dashboard_path = self.save_dir / f"dashboard_{stock_code}.html"
        with open(dashboard_path, 'w', encoding='utf-8') as f:
            f.write(dashboard_html)
        
        logger.info(f"仪表板已生成: {dashboard_path}")
        return str(dashboard_path)
    
    def clear_history(self):
        """清空历史数据"""
        self.episodes.clear()
        self.rewards.clear()
        self.losses.clear()
        self.epsilons.clear()
        self.learning_rates.clear()
        self.performance_history.clear()

if __name__ == "__main__":
    # 测试训练监控器
    monitor = TrainingMonitor()
    
    # 模拟训练数据
    for episode in range(100):
        reward = np.random.randn() + episode * 0.01
        loss = max(0, 1 - episode * 0.01 + np.random.randn() * 0.1)
        epsilon = max(0.01, 1 - episode * 0.01)
        
        monitor.update(episode, reward, loss, epsilon)
    
    # 绘制训练曲线
    fig = monitor.plot_training_curves("TEST")
    
    print("训练监控器测试完成！")
