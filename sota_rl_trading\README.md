# SOTA深度强化学习多因子股票投资系统

## 项目简介

这是一个超越当前SOTA水平的深度强化学习多因子股票投资系统，整合了最新的深度学习和强化学习技术：

### 核心技术特性

1. **层次化动态注意力网络 (H-DAN)**
   - 融合CNN-LSTM-DQN架构
   - 动态分块技术 (Dynamic Chunking)
   - Transformer注意力机制
   - 层次化强化学习

2. **先进的因子工程**
   - 自适应因子选择
   - 多尺度特征提取
   - 基于注意力的因子重要性评估
   - 交互特征生成

3. **风险感知交易系统**
   - VaR/CVaR风险控制
   - Kelly准则仓位管理
   - 多时间尺度决策
   - 实时风险监控

4. **专业级性能评估**
   - 全面的性能指标
   - 基准比较分析
   - 可视化监控面板
   - 回测验证系统

## 系统架构

```
sota_rl_trading/
├── config.py                 # 全局配置管理
├── train.py                  # 主训练脚本
├── data/                     # 数据处理模块
│   ├── loaders/             # 数据加载器
│   └── processors/          # 因子处理器
├── models/                   # 模型架构
│   ├── networks/            # 网络定义
│   └── components/          # 组件模块
├── environments/             # 交易环境
│   └── trading/             # 层次化交易环境
├── agents/                   # 智能体
│   └── hierarchical/        # H-DAN智能体
├── utils/                    # 工具模块
│   ├── risk/                # 风险管理
│   └── metrics/             # 性能指标
├── visualization/            # 可视化工具
├── tests/                    # 测试模块
├── docs/                     # 文档
├── logs/                     # 日志文件
├── checkpoints/              # 模型检查点
└── results/                  # 结果输出
```

## 数据格式要求

系统支持您提供的CSV格式，包含以下字段：

### 必需字段
- `ts_code`: 股票代码
- `trade_date`: 交易日期

### 可选因子字段（根据您的配置选择）
- **基础价格因子**: `open`, `high`, `low`, `close`, `pct_chg`, `vol`, `amount`
- **动量指标**: `momentum_5d`, `momentum_10d`, `momentum_20d`
- **移动平均**: `ma_ratio_5d`, `ma_ratio_10d`, `ma_ratio_20d`
- **波动率**: `volatility_5d`, `volatility_10d`, `volatility_20d`
- **MACD指标**: `macd`, `macd_signal`, `macd_hist`
- **其他技术指标**: `bb_ratio`, `atr`, `adaptive_ma`

**注意**: 系统会根据您在配置文件中选择的因子自动调整模型参数，无需手动匹配。

## 快速开始

### 1. 环境配置

```bash
# 创建虚拟环境
conda create -n sota_trading python=3.9
conda activate sota_trading

# 安装依赖
pip install torch torchvision torchaudio
pip install pandas numpy scikit-learn
pip install matplotlib seaborn plotly
pip install gym scipy
```

### 2. 配置因子

**最重要**: 在 `config.py` 文件中配置您需要的因子：

```python
# 在 config.py 中找到 FactorConfig 类，修改 selected_factors
selected_factors: List[str] = field(default_factory=lambda: [
    # 基础价格因子
    'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount',
    # 您需要的技术指标 - 根据您的CSV文件添加或删除
    'momentum_5d', 'ma_ratio_5d', 'volatility_5d',
    'momentum_10d', 'ma_ratio_10d', 'volatility_10d',
    'momentum_20d', 'ma_ratio_20d', 'volatility_20d',
    'macd', 'macd_signal', 'macd_hist',
    'bb_ratio', 'atr', 'adaptive_ma'
])
```

**系统会自动**：
- 根据因子数量调整CNN输入通道数
- 调整DQN网络状态维度
- 验证数据完整性

### 3. 其他参数配置

```python
# 交易配置
trading_config.initial_capital = 200000.0  # 初始资金
trading_config.execution_price = 'close'  # 收盘价执行
trading_config.execution_timing = 'close'  # 收盘时执行

# 风险管理
risk_management.max_drawdown_threshold = 0.15  # 最大回撤15%
risk_management.volatility_target = 0.15  # 目标波动率15%
```

### 4. 快速配置示例

查看 `config_example.py` 文件了解更多配置示例：

```python
# 基础配置（适合新手）
python config_example.py  # 查看配置示例

# 或者直接在代码中配置
from config import config
config.factor.selected_factors = ['close', 'vol', 'pct_chg', 'momentum_10d', 'macd']
config._auto_adjust_model_params()  # 自动调整模型参数
```

### 5. 数据准备

将您的CSV数据文件放置在 `data/` 目录下，确保包含您配置的因子字段。

### 6. 开始训练

```bash
# 训练所有股票
python train.py --data_path data/your_data.csv

# 训练指定股票
python train.py --data_path data/your_data.csv --stock_codes 000001.SZ 000002.SZ

# 使用自定义配置
python train.py --data_path data/your_data.csv --config_path custom_config.json
```

### 7. 监控训练

训练过程中会自动生成：
- 实时训练曲线
- 性能指标监控
- 注意力权重可视化
- 交易决策分析

查看结果：
```bash
# 打开可视化面板
open visualization/plots/dashboard_STOCK_CODE.html

# 查看训练日志
tail -f logs/training.log
```

## 高级配置

### 动态因子配置

**核心特性**: 系统支持动态因子配置，无需手动调整网络参数！

```python
# 方法1: 直接修改配置文件
# 在 config.py 中修改 selected_factors 列表

# 方法2: 运行时动态配置
from config import config

# 选择您需要的因子
config.factor.selected_factors = [
    'close', 'vol', 'pct_chg',  # 基础因子
    'momentum_10d', 'ma_ratio_10d',  # 技术指标
    'macd', 'bb_ratio'  # 更多指标
]

# 自动调整模型参数
config._auto_adjust_model_params()

# 系统会自动设置:
# - CNN输入通道数 = 因子数量
# - DQN状态维度 = CNN输出 + LSTM输出 + 位置信息
```

### 网络结构调整

```python
# CNN配置（输入通道数会自动调整）
cnn_config = {
    'conv_layers': [
        {'out_channels': 32, 'kernel_size': 3, 'stride': 1, 'padding': 1},
        {'out_channels': 64, 'kernel_size': 3, 'stride': 1, 'padding': 1},
        {'out_channels': 128, 'kernel_size': 3, 'stride': 2, 'padding': 1},
    ],
    'dropout_rate': 0.2,
    'output_features': 8
}

# LSTM配置
lstm_config = {
    'hidden_size': 256,
    'num_layers': 2,
    'attention_enabled': True,
    'attention_dim': 128
}
```

### 交易策略配置

```python
# 仓位管理
position_sizing = {
    'method': 'kelly_criterion',  # Kelly准则
    'kelly_fraction': 0.25,
    'rebalance_frequency': 'daily'
}

# 风险控制
risk_management = {
    'max_drawdown_threshold': 0.15,
    'stop_loss_threshold': 0.05,
    'take_profit_threshold': 0.10,
    'var_confidence_level': 0.05
}
```

## 性能指标

系统提供全面的性能评估指标：

### 收益指标
- 总收益率、年化收益率
- 胜率、盈亏比
- 超额收益、Alpha/Beta

### 风险指标
- 波动率、最大回撤
- VaR、CVaR
- 下行风险、偏度峰度

### 风险调整收益
- 夏普比率、Sortino比率
- Calmar比率、信息比率

### 交易指标
- 交易次数、交易胜率
- 平均盈利/亏损
- 最大单笔盈亏

## 可视化功能

1. **训练监控**
   - 实时奖励/损失曲线
   - Epsilon衰减曲线
   - 学习率变化

2. **注意力分析**
   - 因子重要性热力图
   - 时序注意力权重
   - 动态分块可视化

3. **交易分析**
   - 投资组合净值曲线
   - 回撤分析图
   - 交易点位标记

4. **风险监控**
   - 风险指标仪表板
   - VaR/CVaR监控
   - 相关性分析

## 扩展功能

### 1. 多资产组合
支持多股票组合优化，自动资产配置

### 2. 实时交易
集成实时数据接口，支持实盘交易

### 3. 策略回测
完整的历史回测框架，支持多种基准比较

### 4. 模型集成
支持多模型集成，提高预测稳定性

## 注意事项

1. **数据质量**：确保数据完整性和准确性
2. **计算资源**：建议使用GPU加速训练
3. **风险控制**：严格遵守风险管理规则
4. **参数调优**：根据具体市场调整参数
5. **合规要求**：遵守相关金融法规

## 技术支持

如有问题，请查看：
- 详细文档：`docs/` 目录
- 测试用例：`tests/` 目录
- 日志文件：`logs/` 目录

## 免责声明

本系统仅供研究和教育用途。投资有风险，使用本系统进行实际交易需要您自行承担风险。请在充分理解系统原理和风险的基础上谨慎使用。

---

**SOTA深度强化学习多因子股票投资系统 v1.0.0**  
*基于最新深度学习和强化学习技术的专业级量化交易系统*
