"""
SOTA深度强化学习多因子股票投资系统 - 主训练脚本
"""

import os
import sys
import logging
import argparse
import numpy as np
import pandas as pd
import torch
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from config import config, validate_config
from data.loaders.data_loader import DataLoaderFactory
from data.processors.factor_processor import FactorProcessor
from environments.trading.hierarchical_env import HierarchicalTradingEnvironment
from agents.hierarchical.h_dan_agent import HDANAgent
from utils.metrics.performance_metrics import PerformanceMetrics
from visualization.training_monitor import TrainingMonitor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SOTATradingTrainer:
    """SOTA交易系统训练器"""
    
    def __init__(self, data_path: str, stock_codes: list = None):
        """
        初始化训练器
        
        Args:
            data_path: 数据文件路径
            stock_codes: 股票代码列表，如果为None则使用数据中的所有股票
        """
        self.data_path = data_path
        self.stock_codes = stock_codes
        
        # 验证配置
        validate_config()
        
        # 创建必要目录
        self._create_directories()
        
        # 初始化组件
        self.data_loader = None
        self.factor_processor = FactorProcessor()
        self.performance_metrics = PerformanceMetrics()
        self.training_monitor = TrainingMonitor()
        
        # 训练状态
        self.current_episode = 0
        self.best_performance = -np.inf
        self.training_history = []
        
        logger.info("SOTA交易系统训练器初始化完成")
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            'logs', 'checkpoints', 'results', 'visualization/plots'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def load_and_prepare_data(self):
        """加载和预处理数据"""
        logger.info("开始加载和预处理数据")
        
        # 加载数据
        self.data_loader, raw_data = DataLoaderFactory.load_and_preprocess(self.data_path)
        
        # 因子处理
        processed_data = self.factor_processor.process_factors(raw_data)
        
        # 如果未指定股票代码，使用所有股票
        if self.stock_codes is None:
            self.stock_codes = processed_data['stock_code'].unique().tolist()
            logger.info(f"使用所有股票: {len(self.stock_codes)} 只")
        
        # 数据分割
        self.train_data, self.val_data, self.test_data = self._split_data(processed_data)
        
        logger.info(f"数据加载完成 - 训练: {len(self.train_data)}, 验证: {len(self.val_data)}, 测试: {len(self.test_data)}")
        
        return processed_data
    
    def _split_data(self, data: pd.DataFrame) -> tuple:
        """分割数据"""
        train_start = config.training.train_start_date
        train_end = config.training.train_end_date
        val_start = config.training.val_start_date
        val_end = config.training.val_end_date
        test_start = config.training.test_start_date
        test_end = config.training.test_end_date
        
        train_data = data[
            (data['date'] >= train_start) & (data['date'] <= train_end)
        ].copy()
        
        val_data = data[
            (data['date'] >= val_start) & (data['date'] <= val_end)
        ].copy()
        
        test_data = data[
            (data['date'] >= test_start) & (data['date'] <= test_end)
        ].copy()
        
        return train_data, val_data, test_data
    
    def train_agent(self, stock_code: str) -> dict:
        """训练单个股票的智能体"""
        logger.info(f"开始训练股票 {stock_code} 的智能体")
        
        # 创建环境和智能体
        env = HierarchicalTradingEnvironment(self.train_data, stock_code)
        agent = HDANAgent()
        
        # 训练配置
        episodes = config.training.rl_config['episodes']
        save_frequency = config.system.logging['save_frequency']
        
        episode_rewards = []
        episode_losses = []
        
        for episode in range(episodes):
            # 重置环境
            state = env.reset()
            episode_reward = 0
            episode_loss = 0
            step_count = 0
            
            while True:
                # 选择动作
                action = agent.select_action(state, training=True)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 存储经验
                agent.store_experience(state, action, reward, next_state, done, info)
                
                # 训练智能体
                if len(agent.memory) >= config.training.rl_config['min_memory_size']:
                    loss_info = agent.train_step()
                    if loss_info:
                        episode_loss += loss_info.get('total_loss', 0)
                
                state = next_state
                episode_reward += reward
                step_count += 1
                
                if done:
                    break
            
            episode_rewards.append(episode_reward)
            episode_losses.append(episode_loss / max(step_count, 1))
            
            # 记录训练进度
            if episode % 10 == 0:
                avg_reward = np.mean(episode_rewards[-10:])
                avg_loss = np.mean(episode_losses[-10:])
                
                logger.info(f"股票 {stock_code} - Episode {episode}: "
                          f"平均奖励={avg_reward:.4f}, 平均损失={avg_loss:.4f}, "
                          f"Epsilon={agent.epsilon:.4f}")
                
                # 更新训练监控
                self.training_monitor.update(episode, avg_reward, avg_loss, agent.epsilon)
            
            # 保存检查点
            if episode % save_frequency == 0 and episode > 0:
                checkpoint_path = f"checkpoints/{stock_code}_episode_{episode}.pth"
                agent.save_model(checkpoint_path)
                logger.info(f"保存检查点: {checkpoint_path}")
        
        # 训练完成后的评估
        final_performance = self._evaluate_agent(agent, env, stock_code)
        
        # 保存最终模型
        final_model_path = f"checkpoints/{stock_code}_final.pth"
        agent.save_model(final_model_path)
        
        return {
            'stock_code': stock_code,
            'episode_rewards': episode_rewards,
            'episode_losses': episode_losses,
            'final_performance': final_performance,
            'model_path': final_model_path
        }
    
    def _evaluate_agent(self, agent: HDANAgent, env: HierarchicalTradingEnvironment, 
                       stock_code: str) -> dict:
        """评估智能体性能"""
        logger.info(f"评估股票 {stock_code} 的智能体性能")
        
        # 在验证集上评估
        val_env = HierarchicalTradingEnvironment(self.val_data, stock_code)
        
        state = val_env.reset()
        total_reward = 0
        step_count = 0
        
        while True:
            action = agent.select_action(state, training=False)
            next_state, reward, done, info = val_env.step(action)
            
            total_reward += reward
            step_count += 1
            state = next_state
            
            if done:
                break
        
        # 获取性能摘要
        performance_summary = val_env.get_performance_summary()
        performance_summary['total_reward'] = total_reward
        performance_summary['avg_reward'] = total_reward / max(step_count, 1)
        
        return performance_summary
    
    def train_all_stocks(self):
        """训练所有股票"""
        logger.info(f"开始训练所有股票，共 {len(self.stock_codes)} 只")
        
        all_results = []
        
        for i, stock_code in enumerate(self.stock_codes):
            logger.info(f"训练进度: {i+1}/{len(self.stock_codes)} - {stock_code}")
            
            try:
                result = self.train_agent(stock_code)
                all_results.append(result)
                
                # 保存中间结果
                self._save_intermediate_results(all_results)
                
            except Exception as e:
                logger.error(f"训练股票 {stock_code} 时出错: {e}")
                continue
        
        # 保存最终结果
        self._save_final_results(all_results)
        
        return all_results
    
    def _save_intermediate_results(self, results: list):
        """保存中间结果"""
        results_df = pd.DataFrame([
            {
                'stock_code': r['stock_code'],
                'final_total_return': r['final_performance'].get('total_return', 0),
                'final_sharpe_ratio': r['final_performance'].get('sharpe_ratio', 0),
                'final_max_drawdown': r['final_performance'].get('max_drawdown', 0),
                'avg_episode_reward': np.mean(r['episode_rewards'][-100:]) if r['episode_rewards'] else 0
            }
            for r in results
        ])
        
        results_df.to_csv('results/intermediate_results.csv', index=False)
    
    def _save_final_results(self, results: list):
        """保存最终结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果
        final_results = {
            'timestamp': timestamp,
            'config': config.to_dict(),
            'results': results,
            'summary': self._generate_summary(results)
        }
        
        import json
        with open(f'results/final_results_{timestamp}.json', 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存CSV摘要
        summary_df = pd.DataFrame([
            {
                'stock_code': r['stock_code'],
                'total_return': r['final_performance'].get('total_return', 0),
                'annualized_return': r['final_performance'].get('annualized_return', 0),
                'sharpe_ratio': r['final_performance'].get('sharpe_ratio', 0),
                'max_drawdown': r['final_performance'].get('max_drawdown', 0),
                'win_rate': r['final_performance'].get('win_rate', 0),
                'total_trades': r['final_performance'].get('total_trades', 0)
            }
            for r in results
        ])
        
        summary_df.to_csv(f'results/summary_{timestamp}.csv', index=False)
        
        logger.info(f"最终结果已保存: results/final_results_{timestamp}.json")
    
    def _generate_summary(self, results: list) -> dict:
        """生成训练摘要"""
        if not results:
            return {}
        
        # 提取性能指标
        total_returns = [r['final_performance'].get('total_return', 0) for r in results]
        sharpe_ratios = [r['final_performance'].get('sharpe_ratio', 0) for r in results]
        max_drawdowns = [r['final_performance'].get('max_drawdown', 0) for r in results]
        
        summary = {
            'total_stocks': len(results),
            'avg_total_return': np.mean(total_returns),
            'avg_sharpe_ratio': np.mean(sharpe_ratios),
            'avg_max_drawdown': np.mean(max_drawdowns),
            'best_stock': max(results, key=lambda x: x['final_performance'].get('sharpe_ratio', 0))['stock_code'],
            'worst_stock': min(results, key=lambda x: x['final_performance'].get('sharpe_ratio', 0))['stock_code']
        }
        
        return summary

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SOTA深度强化学习多因子股票投资系统训练')
    parser.add_argument('--data_path', type=str, required=True, help='CSV数据文件路径')
    parser.add_argument('--stock_codes', type=str, nargs='*', help='指定训练的股票代码')
    parser.add_argument('--config_path', type=str, help='配置文件路径')
    
    args = parser.parse_args()
    
    # 加载自定义配置
    if args.config_path:
        config.load_config(args.config_path)
    
    # 设置随机种子
    np.random.seed(config.system.random_seed)
    torch.manual_seed(config.system.random_seed)
    
    # 创建训练器
    trainer = SOTATradingTrainer(args.data_path, args.stock_codes)
    
    try:
        # 加载数据
        trainer.load_and_prepare_data()
        
        # 开始训练
        results = trainer.train_all_stocks()
        
        logger.info("训练完成！")
        logger.info(f"训练了 {len(results)} 只股票")
        
        # 显示摘要
        if results:
            summary = trainer._generate_summary(results)
            logger.info(f"平均总收益率: {summary['avg_total_return']:.2%}")
            logger.info(f"平均夏普比率: {summary['avg_sharpe_ratio']:.2f}")
            logger.info(f"平均最大回撤: {summary['avg_max_drawdown']:.2%}")
            logger.info(f"最佳股票: {summary['best_stock']}")
        
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
