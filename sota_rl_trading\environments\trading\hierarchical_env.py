"""
层次化交易环境 - 支持多时间尺度的强化学习决策
实现日内、日间、周间等不同层次的交易策略
"""

import numpy as np
import pandas as pd
import torch
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
from enum import Enum
import gym
from gym import spaces

from ...config import config
from ...utils.risk.risk_manager import RiskManager
from ...utils.metrics.performance_metrics import PerformanceMetrics

logger = logging.getLogger(__name__)

class ActionType(Enum):
    """动作类型"""
    HOLD = 0
    SELL = 1
    BUY = 2

@dataclass
class TradingState:
    """交易状态"""
    current_price: float
    position: float  # 持仓比例 [-1, 1]
    cash: float
    portfolio_value: float
    features: np.ndarray
    market_features: np.ndarray
    risk_metrics: Dict[str, float]
    time_step: int
    date: str

@dataclass
class HierarchicalAction:
    """层次化动作"""
    daily_action: int
    weekly_action: int
    monthly_action: int
    position_size: float

class HierarchicalTradingEnvironment(gym.Env):
    """层次化交易环境"""
    
    def __init__(self, data: pd.DataFrame, stock_code: str):
        super().__init__()
        
        self.data = data[data['stock_code'] == stock_code].copy()
        self.stock_code = stock_code
        self.config = config.environment
        self.trading_config = config.trading
        
        # 环境参数
        self.initial_capital = self.trading_config.initial_capital
        self.transaction_cost = self.trading_config.transaction_cost
        self.slippage = self.trading_config.slippage
        
        # 时间尺度配置
        self.time_scales = self.config.hierarchical_env['time_scales']
        self.decision_frequencies = self.config.hierarchical_env['decision_frequencies']
        
        # 状态空间
        self.feature_dim = len(config.factor.price_factors) + len(config.factor.technical_factors)
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf,
            shape=(self.feature_dim + 10,),  # 特征 + 状态信息
            dtype=np.float32
        )
        
        # 动作空间 - 层次化动作
        self.action_space = spaces.Dict({
            'daily_action': spaces.Discrete(3),    # Hold, Sell, Buy
            'weekly_action': spaces.Discrete(3),
            'monthly_action': spaces.Discrete(3),
            'position_size': spaces.Box(low=0.0, high=1.0, shape=(1,), dtype=np.float32)
        })
        
        # 初始化组件
        self.risk_manager = RiskManager()
        self.performance_metrics = PerformanceMetrics()
        
        # 环境状态
        self.reset()
    
    def reset(self) -> np.ndarray:
        """重置环境"""
        logger.info(f"重置交易环境: {self.stock_code}")
        
        # 重置数据索引
        self.current_step = 0
        self.max_steps = len(self.data) - 1
        
        # 重置交易状态
        self.cash = self.initial_capital
        self.position = 0.0
        self.portfolio_value = self.initial_capital
        self.total_trades = 0
        
        # 重置层次化决策计数器
        self.daily_step = 0
        self.weekly_step = 0
        self.monthly_step = 0
        
        # 历史记录
        self.trade_history = []
        self.portfolio_history = [self.initial_capital]
        self.position_history = [0.0]
        self.action_history = []
        
        # 风险指标历史
        self.returns_history = []
        self.drawdown_history = []
        
        # 获取初始状态
        return self._get_observation()
    
    def step(self, action: Dict) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行一步"""
        if self.current_step >= self.max_steps:
            return self._get_observation(), 0.0, True, {}
        
        # 解析层次化动作
        hierarchical_action = self._parse_action(action)
        
        # 执行交易
        reward, trade_info = self._execute_trade(hierarchical_action)
        
        # 更新状态
        self.current_step += 1
        self.daily_step += 1
        
        # 更新层次化计数器
        if self.daily_step % self.decision_frequencies[1] == 0:
            self.weekly_step += 1
        if self.daily_step % self.decision_frequencies[2] == 0:
            self.monthly_step += 1
        
        # 记录历史
        self.portfolio_history.append(self.portfolio_value)
        self.position_history.append(self.position)
        self.action_history.append(hierarchical_action)
        
        # 计算收益率
        if len(self.portfolio_history) > 1:
            daily_return = (self.portfolio_history[-1] / self.portfolio_history[-2]) - 1
            self.returns_history.append(daily_return)
        
        # 检查终止条件
        done = self._check_done()
        
        # 构建信息字典
        info = {
            'trade_info': trade_info,
            'portfolio_value': self.portfolio_value,
            'position': self.position,
            'cash': self.cash,
            'total_trades': self.total_trades,
            'current_price': self._get_current_price(),
            'hierarchical_action': hierarchical_action
        }
        
        return self._get_observation(), reward, done, info
    
    def _parse_action(self, action: Dict) -> HierarchicalAction:
        """解析层次化动作"""
        return HierarchicalAction(
            daily_action=action['daily_action'],
            weekly_action=action['weekly_action'],
            monthly_action=action['monthly_action'],
            position_size=float(action['position_size'][0])
        )
    
    def _execute_trade(self, action: HierarchicalAction) -> Tuple[float, Dict]:
        """执行交易"""
        current_price = self._get_current_price()
        
        # 层次化决策融合
        final_action = self._fuse_hierarchical_actions(action)
        
        # 风险检查
        risk_check = self.risk_manager.check_trade_risk(
            action=final_action,
            current_position=self.position,
            portfolio_value=self.portfolio_value,
            current_price=current_price
        )
        
        if not risk_check['allowed']:
            # 风险过高，强制持有
            final_action = ActionType.HOLD.value
            action.position_size = 0.0
        
        # 执行交易
        trade_info = self._process_trade(final_action, action.position_size, current_price)
        
        # 计算奖励
        reward = self._calculate_reward(trade_info, risk_check)
        
        return reward, trade_info
    
    def _fuse_hierarchical_actions(self, action: HierarchicalAction) -> int:
        """融合层次化动作"""
        # 权重配置
        daily_weight = 0.5
        weekly_weight = 0.3
        monthly_weight = 0.2
        
        # 动作得分
        action_scores = np.zeros(3)
        action_scores[action.daily_action] += daily_weight
        action_scores[action.weekly_action] += weekly_weight
        action_scores[action.monthly_action] += monthly_weight
        
        # 选择得分最高的动作
        final_action = np.argmax(action_scores)
        
        return final_action
    
    def _process_trade(self, action: int, position_size: float, current_price: float) -> Dict:
        """处理交易"""
        trade_info = {
            'action': action,
            'position_size': position_size,
            'price': current_price,
            'cost': 0.0,
            'executed': False,
            'old_position': self.position,
            'new_position': self.position
        }
        
        if action == ActionType.HOLD.value:
            # 持有，无操作
            pass
        
        elif action == ActionType.BUY.value and self.position < self.trading_config.max_position_size:
            # 买入
            target_position = min(
                self.position + position_size,
                self.trading_config.max_position_size
            )
            
            position_change = target_position - self.position
            if position_change > 0:
                # 计算交易成本
                trade_value = position_change * self.portfolio_value
                transaction_cost = trade_value * self.transaction_cost
                slippage_cost = trade_value * self.slippage
                total_cost = transaction_cost + slippage_cost
                
                if self.cash >= total_cost:
                    self.position = target_position
                    self.cash -= total_cost
                    self.total_trades += 1
                    
                    trade_info.update({
                        'executed': True,
                        'cost': total_cost,
                        'new_position': self.position
                    })
        
        elif action == ActionType.SELL.value and self.position > self.trading_config.min_position_size:
            # 卖出
            target_position = max(
                self.position - position_size,
                self.trading_config.min_position_size
            )
            
            position_change = self.position - target_position
            if position_change > 0:
                # 计算交易收入
                trade_value = position_change * self.portfolio_value
                transaction_cost = trade_value * self.transaction_cost
                slippage_cost = trade_value * self.slippage
                total_cost = transaction_cost + slippage_cost
                
                self.position = target_position
                self.cash += trade_value - total_cost
                self.total_trades += 1
                
                trade_info.update({
                    'executed': True,
                    'cost': total_cost,
                    'new_position': self.position
                })
        
        # 更新投资组合价值
        self.portfolio_value = self.cash + self.position * current_price * self.initial_capital
        
        return trade_info
    
    def _calculate_reward(self, trade_info: Dict, risk_check: Dict) -> float:
        """计算奖励"""
        reward = 0.0
        
        # 基础收益奖励
        if len(self.portfolio_history) > 1:
            portfolio_return = (self.portfolio_value / self.portfolio_history[-1]) - 1
            reward += self.config.reward_config['return_weight'] * portfolio_return
        
        # 风险惩罚
        if len(self.returns_history) >= 20:
            recent_returns = np.array(self.returns_history[-20:])
            volatility = np.std(recent_returns)
            reward -= self.config.reward_config['risk_penalty_weight'] * volatility
        
        # 交易成本惩罚
        if trade_info['executed']:
            cost_penalty = trade_info['cost'] / self.portfolio_value
            reward -= self.config.reward_config['transaction_cost_penalty'] * cost_penalty
        
        # 回撤惩罚
        if len(self.portfolio_history) > 1:
            peak_value = max(self.portfolio_history)
            current_drawdown = (peak_value - self.portfolio_value) / peak_value
            if current_drawdown > 0:
                reward -= self.config.reward_config['drawdown_penalty_weight'] * current_drawdown
        
        # 夏普比率奖励
        if len(self.returns_history) >= 252:  # 一年数据
            annual_returns = np.array(self.returns_history[-252:])
            if np.std(annual_returns) > 0:
                sharpe_ratio = np.mean(annual_returns) / np.std(annual_returns) * np.sqrt(252)
                reward += self.config.reward_config['sharpe_bonus_weight'] * max(0, sharpe_ratio - 1)
        
        # 风险管理奖励
        if not risk_check['allowed']:
            reward -= 0.1  # 风险过高的惩罚
        
        return reward
    
    def _get_observation(self) -> np.ndarray:
        """获取观测"""
        if self.current_step >= len(self.data):
            # 返回最后一个有效观测
            return self._get_observation_at_step(len(self.data) - 1)
        
        return self._get_observation_at_step(self.current_step)
    
    def _get_observation_at_step(self, step: int) -> np.ndarray:
        """获取指定步骤的观测"""
        row = self.data.iloc[step]
        
        # 基础特征
        price_features = [row[col] for col in config.factor.price_factors if col in row]
        tech_features = [row[col] for col in config.factor.technical_factors if col in row]
        
        # 状态信息
        state_info = [
            self.position,                    # 当前持仓
            self.cash / self.initial_capital, # 现金比例
            self.portfolio_value / self.initial_capital,  # 组合价值比例
            self.total_trades / 100,          # 交易次数（标准化）
            self.current_step / self.max_steps,  # 时间进度
        ]
        
        # 风险指标
        if len(self.returns_history) >= 20:
            recent_returns = np.array(self.returns_history[-20:])
            volatility = np.std(recent_returns)
            max_drawdown = self._calculate_max_drawdown()
        else:
            volatility = 0.0
            max_drawdown = 0.0
        
        risk_info = [
            volatility,
            max_drawdown,
            len(self.returns_history) / 252,  # 数据长度（年）
        ]
        
        # 层次化状态
        hierarchical_info = [
            self.daily_step / self.decision_frequencies[0],
            self.weekly_step / (self.decision_frequencies[1] // self.decision_frequencies[0]),
        ]
        
        # 合并所有特征
        observation = np.array(
            price_features + tech_features + state_info + risk_info + hierarchical_info,
            dtype=np.float32
        )
        
        # 处理NaN值
        observation = np.nan_to_num(observation, nan=0.0, posinf=1.0, neginf=-1.0)
        
        return observation
    
    def _get_current_price(self) -> float:
        """获取当前价格"""
        if self.current_step >= len(self.data):
            return self.data.iloc[-1]['close']
        return self.data.iloc[self.current_step]['close']
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        if len(self.portfolio_history) < 2:
            return 0.0
        
        portfolio_values = np.array(self.portfolio_history)
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (peak - portfolio_values) / peak
        return np.max(drawdown)
    
    def _check_done(self) -> bool:
        """检查是否结束"""
        # 时间结束
        if self.current_step >= self.max_steps:
            return True
        
        # 资金耗尽
        if self.portfolio_value <= self.initial_capital * 0.1:
            logger.warning("资金耗尽，提前结束")
            return True
        
        # 最大回撤超限
        max_drawdown = self._calculate_max_drawdown()
        if max_drawdown > self.trading_config.risk_management['max_drawdown_threshold']:
            logger.warning(f"最大回撤超限: {max_drawdown:.3f}")
            return True
        
        return False
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        if len(self.portfolio_history) < 2:
            return {}
        
        return self.performance_metrics.calculate_metrics(
            portfolio_values=self.portfolio_history,
            returns=self.returns_history,
            positions=self.position_history,
            trades=self.trade_history
        )
    
    def render(self, mode='human'):
        """渲染环境"""
        if mode == 'human':
            print(f"Step: {self.current_step}")
            print(f"Portfolio Value: ${self.portfolio_value:.2f}")
            print(f"Position: {self.position:.3f}")
            print(f"Cash: ${self.cash:.2f}")
            print(f"Total Trades: {self.total_trades}")
            print("-" * 40)

if __name__ == "__main__":
    # 测试环境
    import pandas as pd
    
    # 创建测试数据
    dates = pd.date_range('2020-01-01', periods=100, freq='D')
    test_data = pd.DataFrame({
        'stock_code': ['TEST'] * 100,
        'date': dates,
        'close': np.random.randn(100).cumsum() + 100,
        'open': np.random.randn(100).cumsum() + 100,
        'high': np.random.randn(100).cumsum() + 105,
        'low': np.random.randn(100).cumsum() + 95,
        'volume': np.random.randint(1000, 10000, 100),
        'pct_chg': np.random.randn(100) * 0.02,
        'amount': np.random.randint(100000, 1000000, 100),
    })
    
    # 添加技术指标
    for factor in config.factor.technical_factors:
        test_data[factor] = np.random.randn(100)
    
    # 创建环境
    env = HierarchicalTradingEnvironment(test_data, 'TEST')
    
    # 测试环境
    obs = env.reset()
    print(f"观测空间: {obs.shape}")
    
    # 随机动作测试
    for _ in range(10):
        action = env.action_space.sample()
        obs, reward, done, info = env.step(action)
        print(f"奖励: {reward:.4f}, 完成: {done}")
        
        if done:
            break
    
    print("层次化交易环境测试完成！")
