"""
风险管理器 - 实现VaR、CVaR等风险控制机制
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from scipy import stats
from sklearn.covariance import LedoitWolf

from ...config import config

logger = logging.getLogger(__name__)

class RiskManager:
    """风险管理器"""
    
    def __init__(self):
        self.risk_config = config.trading.risk_management
        self.position_config = config.trading.position_sizing
        
        # 历史数据缓存
        self.returns_history = []
        self.portfolio_values = []
        self.positions_history = []
        
    def check_trade_risk(self, action: int, current_position: float, 
                        portfolio_value: float, current_price: float) -> Dict[str, any]:
        """
        检查交易风险
        
        Args:
            action: 交易动作
            current_position: 当前持仓
            portfolio_value: 组合价值
            current_price: 当前价格
            
        Returns:
            风险检查结果
        """
        risk_check = {
            'allowed': True,
            'reasons': [],
            'risk_metrics': {}
        }
        
        # 最大回撤检查
        if len(self.portfolio_values) > 0:
            max_drawdown = self._calculate_max_drawdown()
            if max_drawdown > self.risk_config['max_drawdown_threshold']:
                risk_check['allowed'] = False
                risk_check['reasons'].append(f"最大回撤超限: {max_drawdown:.3f}")
        
        # 仓位限制检查
        if action == 2:  # 买入
            max_position = self.risk_config.get('max_position_size', 0.95)
            if current_position >= max_position:
                risk_check['allowed'] = False
                risk_check['reasons'].append(f"仓位已达上限: {current_position:.3f}")
        
        # VaR检查
        if len(self.returns_history) >= 20:
            var_95 = self._calculate_var(confidence_level=0.05)
            if abs(var_95) > 0.05:  # 5%的VaR限制
                risk_check['allowed'] = False
                risk_check['reasons'].append(f"VaR风险过高: {var_95:.3f}")
            
            risk_check['risk_metrics']['var_95'] = var_95
        
        # 波动率检查
        if len(self.returns_history) >= 20:
            volatility = np.std(self.returns_history[-20:])
            target_vol = self.risk_config.get('volatility_target', 0.15)
            if volatility > target_vol * 1.5:  # 超过目标波动率1.5倍
                risk_check['allowed'] = False
                risk_check['reasons'].append(f"波动率过高: {volatility:.3f}")
            
            risk_check['risk_metrics']['volatility'] = volatility
        
        return risk_check
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        if len(self.portfolio_values) < 2:
            return 0.0
        
        portfolio_values = np.array(self.portfolio_values)
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (peak - portfolio_values) / peak
        return np.max(drawdown)
    
    def _calculate_var(self, confidence_level: float = 0.05) -> float:
        """计算VaR"""
        if len(self.returns_history) < 10:
            return 0.0
        
        returns = np.array(self.returns_history)
        return np.percentile(returns, confidence_level * 100)
    
    def _calculate_cvar(self, confidence_level: float = 0.05) -> float:
        """计算CVaR (条件VaR)"""
        if len(self.returns_history) < 10:
            return 0.0
        
        returns = np.array(self.returns_history)
        var = self._calculate_var(confidence_level)
        cvar = returns[returns <= var].mean()
        return cvar
    
    def calculate_position_size(self, expected_return: float, volatility: float, 
                              current_portfolio_value: float) -> float:
        """
        计算仓位大小
        
        Args:
            expected_return: 预期收益率
            volatility: 波动率
            current_portfolio_value: 当前组合价值
            
        Returns:
            建议仓位大小
        """
        method = self.position_config['method']
        
        if method == 'kelly_criterion':
            return self._kelly_position_size(expected_return, volatility)
        elif method == 'equal_weight':
            return self.position_config.get('default_weight', 0.1)
        elif method == 'risk_parity':
            return self._risk_parity_position_size(volatility)
        else:
            return 0.1  # 默认10%
    
    def _kelly_position_size(self, expected_return: float, volatility: float) -> float:
        """Kelly准则仓位计算"""
        if volatility <= 0:
            return 0.0
        
        # Kelly公式: f = (bp - q) / b
        # 简化为: f = expected_return / variance
        kelly_fraction = expected_return / (volatility ** 2)
        
        # 应用Kelly分数限制
        max_kelly = self.position_config.get('kelly_fraction', 0.25)
        kelly_fraction = np.clip(kelly_fraction, 0, max_kelly)
        
        return kelly_fraction
    
    def _risk_parity_position_size(self, volatility: float) -> float:
        """风险平价仓位计算"""
        if volatility <= 0:
            return 0.0
        
        # 简化的风险平价：权重与波动率成反比
        target_risk = 0.1  # 目标风险贡献
        position_size = target_risk / volatility
        
        return np.clip(position_size, 0, 0.5)
    
    def update_history(self, portfolio_value: float, position: float, 
                      daily_return: float = None):
        """更新历史数据"""
        self.portfolio_values.append(portfolio_value)
        self.positions_history.append(position)
        
        if daily_return is not None:
            self.returns_history.append(daily_return)
        
        # 限制历史数据长度
        max_history = 1000
        if len(self.portfolio_values) > max_history:
            self.portfolio_values = self.portfolio_values[-max_history:]
            self.positions_history = self.positions_history[-max_history:]
            self.returns_history = self.returns_history[-max_history:]
    
    def get_risk_metrics(self) -> Dict[str, float]:
        """获取风险指标"""
        metrics = {}
        
        if len(self.returns_history) >= 10:
            returns = np.array(self.returns_history)
            
            # 基础统计
            metrics['mean_return'] = np.mean(returns)
            metrics['volatility'] = np.std(returns)
            metrics['skewness'] = stats.skew(returns)
            metrics['kurtosis'] = stats.kurtosis(returns)
            
            # 风险指标
            metrics['var_95'] = self._calculate_var(0.05)
            metrics['var_99'] = self._calculate_var(0.01)
            metrics['cvar_95'] = self._calculate_cvar(0.05)
            metrics['cvar_99'] = self._calculate_cvar(0.01)
            
            # 最大回撤
            metrics['max_drawdown'] = self._calculate_max_drawdown()
            
            # 夏普比率
            if metrics['volatility'] > 0:
                metrics['sharpe_ratio'] = metrics['mean_return'] / metrics['volatility'] * np.sqrt(252)
            else:
                metrics['sharpe_ratio'] = 0.0
            
            # Sortino比率
            downside_returns = returns[returns < 0]
            if len(downside_returns) > 0:
                downside_deviation = np.std(downside_returns)
                metrics['sortino_ratio'] = metrics['mean_return'] / downside_deviation * np.sqrt(252)
            else:
                metrics['sortino_ratio'] = 0.0
        
        return metrics

if __name__ == "__main__":
    # 测试风险管理器
    risk_manager = RiskManager()
    
    # 模拟数据
    for i in range(100):
        portfolio_value = 1000000 * (1 + np.random.randn() * 0.01)
        position = np.random.uniform(0, 0.5)
        daily_return = np.random.randn() * 0.02
        
        risk_manager.update_history(portfolio_value, position, daily_return)
    
    # 测试风险检查
    risk_check = risk_manager.check_trade_risk(2, 0.3, 1000000, 100)
    print(f"风险检查结果: {risk_check}")
    
    # 测试风险指标
    risk_metrics = risk_manager.get_risk_metrics()
    print(f"风险指标: {risk_metrics}")
    
    print("风险管理器测试完成！")
